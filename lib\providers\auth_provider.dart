import 'package:flutter/material.dart';
import '../models/pin.dart';
import '../services/api_service.dart';

class AuthProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isLoggedIn = false;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;

  AuthProvider() {
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    try {
      _isLoading = true;
      notifyListeners();

      final isLoggedIn = await ApiService.isLoggedIn();
      if (isLoggedIn) {
        final userData = await ApiService.getCurrentUser();
        _currentUser = User(
          id: userData['id'].toString(),
          username: userData['username'],
          displayName: userData['display_name'],
          avatarUrl: userData['avatar_url'] ?? 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          bio: userData['bio'],
          website: userData['website'],
          followerCount: userData['follower_count'] ?? 0,
          followingCount: userData['following_count'] ?? 0,
        );
        _isLoggedIn = true;
      }
    } catch (e) {
      debugPrint('Error checking login status: $e');
      _isLoggedIn = false;
      _currentUser = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> login(String username, String password) async {
    try {
      _isLoading = true;
      notifyListeners();

      await ApiService.login(username: username, password: password);
      await _checkLoginStatus();
      return _isLoggedIn;
    } catch (e) {
      debugPrint('Login error: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    await ApiService.logout();
    _currentUser = null;
    _isLoggedIn = false;
    notifyListeners();
  }

  Future<bool> register({
    required String username,
    required String email,
    required String displayName,
    required String password,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      await ApiService.register(
        username: username,
        email: email,
        displayName: displayName,
        password: password,
      );

      // Auto login after registration
      return await login(username, password);
    } catch (e) {
      debugPrint('Registration error: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
