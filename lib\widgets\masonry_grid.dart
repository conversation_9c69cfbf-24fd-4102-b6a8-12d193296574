import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../models/pin.dart';
import '../widgets/pin_card.dart';

class MasonryGrid extends StatelessWidget {
  final List<Pin> pins;

  const MasonryGrid({
    super.key,
    required this.pins,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: MasonryGridView.count(
        crossAxisCount: _getCrossAxisCount(context),
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: pins.length,
        itemBuilder: (context, index) {
          return PinCard(pin: pins[index]);
        },
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth > 1200) {
      return 6; // Desktop - 6 columns
    } else if (screenWidth > 800) {
      return 4; // Tablet landscape - 4 columns
    } else if (screenWidth > 600) {
      return 3; // Tablet portrait - 3 columns
    } else {
      return 2; // Mobile - 2 columns
    }
  }
}
