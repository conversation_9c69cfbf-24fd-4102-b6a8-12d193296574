import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../models/pin.dart';
import '../widgets/pin_card.dart';

class MasonryGrid extends StatelessWidget {
  final List<Pin> pins;

  const MasonryGrid({
    super.key,
    required this.pins,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = _getCrossAxisCount(context);
    final spacing = _getSpacing(screenWidth);
    final padding = _getPadding(screenWidth);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: padding),
      child: MasonryGridView.count(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: spacing,
        crossAxisSpacing: spacing,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: pins.length,
        itemBuilder: (context, index) {
          return PinCard(pin: pins[index]);
        },
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1536) {
      return 8; // Very large desktop - 8 columns
    } else if (screenWidth > 1200) {
      return 6; // Large desktop - 6 columns
    } else if (screenWidth > 900) {
      return 5; // Medium desktop - 5 columns
    } else if (screenWidth > 744) {
      return 4; // Small desktop/large tablet - 4 columns
    } else if (screenWidth > 564) {
      return 3; // Tablet - 3 columns
    } else {
      return 2; // Mobile - 2 columns
    }
  }

  double _getSpacing(double screenWidth) {
    if (screenWidth > 744) {
      return 16.0; // Desktop spacing
    } else {
      return 8.0; // Mobile spacing
    }
  }

  double _getPadding(double screenWidth) {
    if (screenWidth > 1200) {
      return 24.0; // Large desktop
    } else if (screenWidth > 744) {
      return 16.0; // Desktop/tablet
    } else {
      return 8.0; // Mobile
    }
  }
}
