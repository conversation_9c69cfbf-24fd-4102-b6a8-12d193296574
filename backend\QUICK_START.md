# Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Option 1: Docker (Recommended)

1. **<PERSON><PERSON> and navigate to backend:**
```bash
cd backend
```

2. **Start with Docker Compose:**
```bash
docker-compose up --build
```

3. **Access the API:**
- API: http://localhost:8000
- Docs: http://localhost:8000/docs
- Database: PostgreSQL on localhost:5432

### Option 2: Local Development

1. **Install dependencies:**
```bash
cd backend
pip install -r requirements.txt
```

2. **Set up PostgreSQL:**
```bash
# Install PostgreSQL and create database
createdb pinterest_clone
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your database URL
```

4. **Run migrations:**
```bash
alembic upgrade head
```

5. **Create sample data:**
```bash
python scripts/create_sample_data.py
```

6. **Start the server:**
```bash
python run.py
```

## 🧪 Test the API

### 1. Register a user:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "Test User",
    "password": "password123"
  }'
```

### 2. Login:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=password123"
```

### 3. Get pins:
```bash
curl "http://localhost:8000/api/v1/pins/"
```

### 4. Create a pin (with auth token):
```bash
curl -X POST "http://localhost:8000/api/v1/pins/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "My First Pin",
    "description": "This is a test pin",
    "image_url": "https://example.com/image.jpg",
    "aspect_ratio": 0.75
  }'
```

## 📱 Connect to Flutter

Update your Flutter app's API base URL to:
```dart
const String baseUrl = 'http://localhost:8000/api/v1';
```

## 🔧 Key Features Implemented

✅ **Authentication**
- JWT-based auth with OAuth2 Password flow
- User registration and login
- Protected routes

✅ **Pins Management**
- Create, read, update, delete pins
- Image upload support
- Like/unlike functionality
- Comments system

✅ **Boards Management**
- Create and manage boards
- Add/remove pins from boards
- Public/private boards

✅ **User Profiles**
- User profile management
- Follow/unfollow users
- User statistics

✅ **Image Upload**
- Local storage support
- AWS S3 integration ready
- Image metadata extraction

✅ **Database**
- PostgreSQL with SQLAlchemy
- Proper relationships and constraints
- Database migrations with Alembic

## 🌐 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/auth/register` | Register user |
| POST | `/api/v1/auth/login` | Login user |
| GET | `/api/v1/auth/me` | Get current user |
| GET | `/api/v1/pins/` | Get pins feed |
| POST | `/api/v1/pins/` | Create pin |
| GET | `/api/v1/pins/{id}` | Get pin details |
| POST | `/api/v1/pins/{id}/like` | Like/unlike pin |
| GET | `/api/v1/boards/user/{id}` | Get user boards |
| POST | `/api/v1/boards/` | Create board |
| POST | `/api/v1/upload/image` | Upload image |

## 🔐 Sample Users (if you ran create_sample_data.py)

| Username | Email | Password |
|----------|-------|----------|
| sarah_designs | <EMAIL> | password123 |
| foodie_mike | <EMAIL> | password123 |
| travel_emma | <EMAIL> | password123 |

## 🚀 Deploy to Production

### Railway:
1. Connect GitHub repo to Railway
2. Set environment variables
3. Deploy automatically

### Render:
1. Create Web Service
2. Build: `pip install -r requirements.txt`
3. Start: `gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT`

### AWS EC2:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 Next Steps

1. **Connect Flutter app** to this backend
2. **Add real image URLs** or set up AWS S3
3. **Deploy to production** using Railway/Render
4. **Add real-time features** with WebSockets
5. **Implement search** functionality
6. **Add email verification**
7. **Set up monitoring** and logging

## 🐛 Troubleshooting

**Database connection issues:**
- Make sure PostgreSQL is running
- Check DATABASE_URL in .env

**Import errors:**
- Make sure you're in the backend directory
- Install requirements: `pip install -r requirements.txt`

**Docker issues:**
- Make sure Docker is running
- Try: `docker-compose down && docker-compose up --build`

## 📞 Support

Check the full README.md for detailed documentation and deployment guides.
