#!/usr/bin/env python3
"""
Script to create sample data for development
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.models import Base, User, Pin, Board
from app.core.security import get_password_hash

def create_sample_data():
    """Create sample users, pins, and boards"""
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Create sample users
        users_data = [
            {
                "username": "sarah_designs",
                "email": "<EMAIL>",
                "display_name": "<PERSON>",
                "bio": "Interior designer & home decor enthusiast",
                "avatar_url": "https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face",
                "password": "password123"
            },
            {
                "username": "foodie_mike",
                "email": "<EMAIL>",
                "display_name": "<PERSON>",
                "bio": "Chef & food photographer",
                "avatar_url": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
                "password": "password123"
            },
            {
                "username": "travel_emma",
                "email": "<EMAIL>",
                "display_name": "Emma <PERSON>",
                "bio": "Travel blogger & photographer",
                "avatar_url": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
                "password": "password123"
            }
        ]
        
        users = []
        for user_data in users_data:
            # Check if user already exists
            existing_user = db.query(User).filter(User.username == user_data["username"]).first()
            if not existing_user:
                user = User(
                    username=user_data["username"],
                    email=user_data["email"],
                    display_name=user_data["display_name"],
                    bio=user_data["bio"],
                    avatar_url=user_data["avatar_url"],
                    hashed_password=get_password_hash(user_data["password"])
                )
                db.add(user)
                users.append(user)
            else:
                users.append(existing_user)
        
        db.commit()
        
        # Create sample pins
        pins_data = [
            {
                "title": "Modern Living Room Design",
                "description": "Minimalist living room with neutral colors and natural lighting",
                "image_url": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=600",
                "aspect_ratio": 0.75,
                "user_index": 0
            },
            {
                "title": "Delicious Pasta Recipe",
                "description": "Creamy mushroom pasta with fresh herbs",
                "image_url": "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=500",
                "aspect_ratio": 0.8,
                "user_index": 1
            },
            {
                "title": "Mountain Landscape",
                "description": "Breathtaking view of snow-capped mountains",
                "image_url": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=700",
                "aspect_ratio": 0.6,
                "user_index": 2
            },
            {
                "title": "Bedroom Decor Ideas",
                "description": "Cozy bedroom with warm lighting and plants",
                "image_url": "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=400",
                "aspect_ratio": 1.0,
                "user_index": 0
            },
            {
                "title": "Healthy Breakfast Bowl",
                "description": "Acai bowl with fresh fruits and granola",
                "image_url": "https://images.unsplash.com/photo-**********-a2132b4ba21d?w=400&h=600",
                "aspect_ratio": 0.67,
                "user_index": 1
            },
            {
                "title": "Ocean Sunset",
                "description": "Beautiful sunset over the ocean waves",
                "image_url": "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=800",
                "aspect_ratio": 0.5,
                "user_index": 2
            }
        ]
        
        pins = []
        for pin_data in pins_data:
            pin = Pin(
                title=pin_data["title"],
                description=pin_data["description"],
                image_url=pin_data["image_url"],
                aspect_ratio=pin_data["aspect_ratio"],
                user_id=users[pin_data["user_index"]].id
            )
            db.add(pin)
            pins.append(pin)
        
        db.commit()
        
        # Create sample boards
        boards_data = [
            {
                "name": "Home Decor",
                "description": "Beautiful home decoration ideas",
                "user_index": 0
            },
            {
                "name": "Recipes",
                "description": "Delicious recipes to try",
                "user_index": 1
            },
            {
                "name": "Travel",
                "description": "Amazing places to visit",
                "user_index": 2
            }
        ]
        
        for board_data in boards_data:
            board = Board(
                name=board_data["name"],
                description=board_data["description"],
                user_id=users[board_data["user_index"]].id
            )
            db.add(board)
        
        db.commit()
        
        print("Sample data created successfully!")
        print("Users created:")
        for user in users:
            print(f"  - {user.username} ({user.email})")
        print(f"Pins created: {len(pins)}")
        print(f"Boards created: {len(boards_data)}")
        print("\nYou can login with any user using password: password123")
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_data()
