# Supabase Database
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.krnaiyjplgzawfrbapji.supabase.co:5432/postgres
SUPABASE_URL=https://krnaiyjplgzawfrbapji.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtybmFpeWpwbGd6YXdmcmJhcGppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyOTcwMDIsImV4cCI6MjA2Njg3MzAwMn0.4xCnhn4XaU5DRevqkO4nnJpR0LRGzQ-rk-vDCiwru6M

# JWT
SECRET_KEY=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Im1wQkJhdENXWXBZZlhOSFFCcElKMiJ9.eyJpc3MiOiJodHRwczovL2Rldi16Yno4Y3ZkaHZudHdsbWNzLnVzLmF1dGgwLmNvbS8iLCJzdWIiOiJic1ZZSHNXR2VUSGRwMGx4NzVrQmUxa0Rqc244c25mRkBjbGllbnRzIiwiYXVkIjoiaHR0cHM6Ly9kZXYtemJ6OGN2ZGh2bnR3bG1jcy51cy5hdXRoMC5jb20vYXBpL3YyLyIsImlhdCI6MTc1MTI5NjU2MiwiZXhwIjoxNzUxMzgyOTYyLCJndHkiOiJjbGllbnQtY3JlZGVudGlhbHMiLCJhenAiOiJic1ZZSHNXR2VUSGRwMGx4NzVrQmUxa0Rqc244c25mRiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AWS S3 (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-bucket-name
AWS_REGION=us-east-1

# App Settings
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
