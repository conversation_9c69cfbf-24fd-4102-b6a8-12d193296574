from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel

from app.schemas.user import UserInDB
from app.schemas.pin import Pin

# Board Schemas
class BoardBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_secret: bool = False

class BoardCreate(BoardBase):
    pass

class BoardUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_secret: Optional[bool] = None

class BoardInDB(BoardBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Board(BoardInDB):
    user: UserInDB
    pin_count: int = 0
    preview_pins: List[Pin] = []

class BoardWithPins(Board):
    pins: List[Pin] = []

class BoardList(BaseModel):
    boards: List[Board]
    total: int

# Board Pin Operations
class AddPinToBoard(BaseModel):
    pin_id: int

class RemovePinFromBoard(BaseModel):
    pin_id: int
