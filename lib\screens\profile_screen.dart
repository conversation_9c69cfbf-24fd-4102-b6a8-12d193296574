import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../models/pin.dart';
import '../services/api_service.dart';
import '../theme/app_theme.dart';
import '../widgets/masonry_grid.dart';

class ProfileScreen extends StatefulWidget {
  final String username;

  const ProfileScreen({
    super.key,
    required this.username,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  User? _profileUser;
  List<Pin> _userPins = [];
  List<Board> _userBoards = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load user profile
      final userData = await ApiService.getUserProfile(widget.username);
      _profileUser = User(
        id: userData['id'].toString(),
        username: userData['username'],
        displayName: userData['display_name'],
        avatarUrl: userData['avatar_url'] ?? 'https://picsum.photos/150/150?random=1',
        bio: userData['bio'],
        website: userData['website'],
        followerCount: userData['follower_count'] ?? 0,
        followingCount: userData['following_count'] ?? 0,
        isFollowing: userData['is_following'] ?? false,
      );

      // Load user pins
      final pinsData = await ApiService.getPins(userId: int.parse(_profileUser!.id));
      final pinsList = pinsData['pins'] as List;
      _userPins = pinsList.map((pinData) => Pin(
        id: pinData['id'].toString(),
        imageUrl: pinData['image_url'],
        title: pinData['title'] ?? '',
        description: pinData['description'] ?? '',
        user: _profileUser!,
        aspectRatio: pinData['aspect_ratio']?.toDouble() ?? 1.0,
        createdAt: DateTime.parse(pinData['created_at']),
        saveCount: pinData['like_count'] ?? 0,
      )).toList();

      // Load user boards
      final boardsData = await ApiService.getUserBoards(int.parse(_profileUser!.id));
      final boardsList = boardsData['boards'] as List;
      _userBoards = boardsList.map((boardData) => Board(
        id: boardData['id'].toString(),
        name: boardData['name'],
        description: boardData['description'] ?? '',
        pins: [],
        owner: _profileUser!,
        createdAt: DateTime.parse(boardData['created_at']),
      )).toList();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading profile: $e');
      // Fallback to sample data
      _loadSampleData();
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _loadSampleData() {
    // Fallback sample data
    _profileUser = User(
      id: '1',
      username: widget.username,
      displayName: 'Sample User',
      avatarUrl: 'https://picsum.photos/150/150?random=1',
      bio: 'This is a sample user profile',
      followerCount: 1250,
      followingCount: 890,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Handle share profile
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz),
            onPressed: () {
              // Handle more options
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _profileUser == null
              ? const Center(child: Text('User not found'))
              : _buildProfileContent(),
    );
  }

  Widget _buildProfileContent() {
    return CustomScrollView(
      slivers: [
        // Profile header
        SliverToBoxAdapter(
          child: _buildProfileHeader(context, _profileUser!),
        ),

        // Tab bar
        SliverToBoxAdapter(
          child: _buildTabBar(),
        ),

        // Tab content
        SliverToBoxAdapter(
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.7,
            child: TabBarView(
              controller: _tabController,
              children: [
                // Created pins
                _userPins.isNotEmpty
                    ? MasonryGrid(pins: _userPins)
                    : const Center(
                        child: Text('No pins yet'),
                      ),

                // Saved pins (boards)
                _buildBoardsGrid(context),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileHeader(BuildContext context, User user) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Cover photo area
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppTheme.backgroundSecondary,
              borderRadius: BorderRadius.circular(16),
              image: user.coverImageUrl != null
                  ? DecorationImage(
                      image: CachedNetworkImageProvider(user.coverImageUrl!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: user.coverImageUrl == null
                ? const Center(
                    child: Icon(
                      Icons.image_outlined,
                      size: 48,
                      color: AppTheme.textSecondary,
                    ),
                  )
                : null,
          ),
          
          const SizedBox(height: 16),
          
          // Profile picture
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: AppTheme.cardShadow,
              image: DecorationImage(
                image: CachedNetworkImageProvider(user.avatarUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // User name and username
          Text(
            user.displayName,
            style: Theme.of(context).textTheme.headlineMedium,
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            '@${user.username}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 12),
          
          // Bio
          if (user.bio != null)
            Text(
              user.bio!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          
          const SizedBox(height: 16),
          
          // Follower stats
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatItem(
                context,
                '${user.followerCount}',
                'followers',
              ),
              const SizedBox(width: 32),
              _buildStatItem(
                context,
                '${user.followingCount}',
                'following',
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!user.isFollowing) ...[
                ElevatedButton(
                  onPressed: () {
                    // Handle follow
                  },
                  child: const Text('Follow'),
                ),
                const SizedBox(width: 12),
              ],
              OutlinedButton(
                onPressed: () {
                  // Handle message
                },
                child: Text(user.isFollowing ? 'Message' : 'Share'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String count, String label) {
    return Column(
      children: [
        Text(
          count,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.textPrimary,
        unselectedLabelColor: AppTheme.textSecondary,
        indicatorColor: AppTheme.pinterestRed,
        indicatorWeight: 3,
        tabs: const [
          Tab(text: 'Created'),
          Tab(text: 'Saved'),
        ],
      ),
    );
  }

  Widget _buildBoardsGrid(BuildContext context) {
    if (_userBoards.isEmpty) {
      return const Center(
        child: Text('No boards yet'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: _userBoards.length,
        itemBuilder: (context, index) {
          final board = _userBoards[index];
          return _buildBoardCard(context, board);
        },
      ),
    );
  }

  Widget _buildBoardCard(BuildContext context, Board board) {
    // Use sample images if no pins in board
    final previewImages = board.pins.isNotEmpty
        ? board.pins.take(3).map((pin) => pin.imageUrl).toList()
        : [
            'https://picsum.photos/200/200?random=30',
            'https://picsum.photos/200/200?random=31',
            'https://picsum.photos/200/200?random=32',
          ];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.cardBackground,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Board preview
            Expanded(
              child: Stack(
                children: [
                  // Main image
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: previewImages[0],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppTheme.backgroundSecondary,
                        child: const Icon(Icons.image, color: AppTheme.textSecondary),
                      ),
                    ),
                  ),

                  // Small preview images in corner
                  if (previewImages.length > 1)
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: previewImages
                            .skip(1)
                            .take(2)
                            .map((url) => Container(
                                  width: 24,
                                  height: 24,
                                  margin: const EdgeInsets.only(left: 4),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    image: DecorationImage(
                                      image: CachedNetworkImageProvider(url),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                ],
              ),
            ),

            // Board info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    board.name,
                    style: const TextStyle(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${board.pins.length} pins',
                    style: const TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
