import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../models/pin.dart';
import '../providers/pin_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/masonry_grid.dart';

class ProfileScreen extends StatefulWidget {
  final String username;

  const ProfileScreen({
    super.key,
    required this.username,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Handle share profile
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz),
            onPressed: () {
              // Handle more options
            },
          ),
        ],
      ),
      body: Consumer<PinProvider>(
        builder: (context, pinProvider, child) {
          final user = pinProvider.getUserByUsername(widget.username);
          
          if (user == null) {
            return const Center(
              child: Text('User not found'),
            );
          }

          final userPins = pinProvider.getPinsByUser(user.id);

          return CustomScrollView(
            slivers: [
              // Profile header
              SliverToBoxAdapter(
                child: _buildProfileHeader(context, user),
              ),
              
              // Tab bar
              SliverToBoxAdapter(
                child: _buildTabBar(),
              ),
              
              // Tab content
              SliverToBoxAdapter(
                child: SizedBox(
                  height: MediaQuery.of(context).size.height * 0.7,
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Created pins
                      userPins.isNotEmpty
                          ? MasonryGrid(pins: userPins)
                          : const Center(
                              child: Text('No pins yet'),
                            ),
                      
                      // Saved pins (boards)
                      _buildBoardsGrid(context),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, User user) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Cover photo area
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppTheme.backgroundSecondary,
              borderRadius: BorderRadius.circular(16),
              image: user.coverImageUrl != null
                  ? DecorationImage(
                      image: CachedNetworkImageProvider(user.coverImageUrl!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: user.coverImageUrl == null
                ? const Center(
                    child: Icon(
                      Icons.image_outlined,
                      size: 48,
                      color: AppTheme.textSecondary,
                    ),
                  )
                : null,
          ),
          
          const SizedBox(height: 16),
          
          // Profile picture
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: AppTheme.cardShadow,
              image: DecorationImage(
                image: CachedNetworkImageProvider(user.avatarUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // User name and username
          Text(
            user.displayName,
            style: Theme.of(context).textTheme.headlineMedium,
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            '@${user.username}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 12),
          
          // Bio
          if (user.bio != null)
            Text(
              user.bio!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          
          const SizedBox(height: 16),
          
          // Follower stats
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatItem(
                context,
                '${user.followerCount}',
                'followers',
              ),
              const SizedBox(width: 32),
              _buildStatItem(
                context,
                '${user.followingCount}',
                'following',
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!user.isFollowing) ...[
                ElevatedButton(
                  onPressed: () {
                    // Handle follow
                  },
                  child: const Text('Follow'),
                ),
                const SizedBox(width: 12),
              ],
              OutlinedButton(
                onPressed: () {
                  // Handle message
                },
                child: Text(user.isFollowing ? 'Message' : 'Share'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String count, String label) {
    return Column(
      children: [
        Text(
          count,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.textPrimary,
        unselectedLabelColor: AppTheme.textSecondary,
        indicatorColor: AppTheme.pinterestRed,
        indicatorWeight: 3,
        tabs: const [
          Tab(text: 'Created'),
          Tab(text: 'Saved'),
        ],
      ),
    );
  }

  Widget _buildBoardsGrid(BuildContext context) {
    // Sample boards data
    final sampleBoards = [
      {
        'name': 'Home Decor',
        'pinCount': 45,
        'previewImages': [
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200',
          'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=200',
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=200&h=200',
        ],
      },
      {
        'name': 'Recipes',
        'pinCount': 23,
        'previewImages': [
          'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=200&h=200',
          'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=200&h=200',
          'https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?w=200&h=200',
        ],
      },
      {
        'name': 'Travel',
        'pinCount': 67,
        'previewImages': [
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200',
          'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=200&h=200',
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200',
        ],
      },
    ];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: sampleBoards.length,
        itemBuilder: (context, index) {
          final board = sampleBoards[index];
          return _buildBoardCard(context, board);
        },
      ),
    );
  }

  Widget _buildBoardCard(BuildContext context, Map<String, dynamic> board) {
    final previewImages = board['previewImages'] as List<String>;
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.cardShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Board preview
            Expanded(
              child: Stack(
                children: [
                  // Main image
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: previewImages[0],
                      fit: BoxFit.cover,
                    ),
                  ),
                  
                  // Small preview images in corner
                  if (previewImages.length > 1)
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: previewImages
                            .skip(1)
                            .take(2)
                            .map((url) => Container(
                                  width: 24,
                                  height: 24,
                                  margin: const EdgeInsets.only(left: 4),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    image: DecorationImage(
                                      image: CachedNetworkImageProvider(url),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                ],
              ),
            ),
            
            // Board info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    board['name'] as String,
                    style: Theme.of(context).textTheme.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${board['pinCount']} pins',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
