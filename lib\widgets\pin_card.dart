import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../models/pin.dart';
import '../theme/app_theme.dart';

class PinCard extends StatefulWidget {
  final Pin pin;

  const PinCard({
    super.key,
    required this.pin,
  });

  @override
  State<PinCard> createState() => _PinCardState();
}

class _PinCardState extends State<PinCard>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _animationController;

  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    

    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTap: () => context.go('/pin/${widget.pin.id}'),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: AppTheme.cardBackground,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pin Image with overlay
                    Stack(
                      children: [
                        _buildPinImage(),
                        if (_isHovered) _buildHoverOverlay(),
                      ],
                    ),

                    // Pin Info
                    _buildPinInfo(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPinImage() {
    return AspectRatio(
      aspectRatio: widget.pin.aspectRatio,
      child: CachedNetworkImage(
        imageUrl: widget.pin.imageUrl,
        fit: BoxFit.cover,
        width: double.infinity,
        placeholder: (context, url) => Container(
          color: AppTheme.backgroundSecondary,
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: AppTheme.textSecondary,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: AppTheme.backgroundSecondary,
          child: const Icon(
            Icons.error_outline,
            color: AppTheme.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildHoverOverlay() {
    return Positioned.fill(
      child: AnimatedOpacity(
        opacity: _opacityAnimation.value,
        duration: const Duration(milliseconds: 200),
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0x4D000000), // Black with 30% opacity
            borderRadius: BorderRadius.circular(16),
          ),
          child: Stack(
            children: [
              // Save button in top right
              Positioned(
                top: 12,
                right: 12,
                child: _buildSaveButton(),
              ),

              // More options in top left
              Positioned(
                top: 12,
                left: 12,
                child: _buildMoreButton(),
              ),

              // Visit button in bottom right (if destination URL exists)
              if (widget.pin.destinationUrl != null)
                Positioned(
                  bottom: 12,
                  right: 12,
                  child: _buildVisitButton(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.pinterestRed,
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Text(
        'Save',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildMoreButton() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.more_horiz,
        size: 20,
        color: AppTheme.textPrimary,
      ),
    );
  }

  Widget _buildVisitButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.arrow_outward,
            size: 16,
            color: AppTheme.textPrimary,
          ),
          SizedBox(width: 4),
          Text(
            'Visit',
            style: TextStyle(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPinInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Pin title
          if (widget.pin.title.isNotEmpty)
            Text(
              widget.pin.title,
              style: const TextStyle(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

          const SizedBox(height: 8),

          // User info
          Row(
            children: [
              // User avatar
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(widget.pin.user.avatarUrl),
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // User name
              Expanded(
                child: Text(
                  widget.pin.user.displayName,
                  style: const TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}


