# Pinterest Clone Backend

A FastAPI-based backend for a Pinterest-style social media platform.

## Features

- **Authentication**: JWT-based authentication with OAuth2 Password flow
- **User Management**: User registration, login, profile management
- **Pins**: Create, read, update, delete pins with image upload
- **Boards**: Organize pins into boards
- **Social Features**: Follow users, like pins, comment on pins
- **Image Upload**: Support for AWS S3 and local storage
- **Database**: Supabase (PostgreSQL) with SQLAlchemy ORM
- **API Documentation**: Auto-generated with FastAPI

## Tech Stack

- **Framework**: FastAPI
- **Database**: PostgreSQL
- **ORM**: SQLAlchemy
- **Authentication**: JWT with python-jose
- **Image Storage**: AWS S3 or Local Storage
- **Testing**: Pytest
- **Deployment**: Docker

## Project Structure

```
backend/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/
│   │       │   ├── auth.py
│   │       │   ├── users.py
│   │       │   ├── pins.py
│   │       │   ├── boards.py
│   │       │   └── upload.py
│   │       └── api.py
│   ├── core/
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── security.py
│   │   └── deps.py
│   ├── models/
│   │   └── models.py
│   ├── schemas/
│   │   ├── user.py
│   │   ├── pin.py
│   │   └── board.py
│   ├── services/
│   │   ├── auth.py
│   │   ├── user.py
│   │   ├── pin.py
│   │   ├── board.py
│   │   ├── comment.py
│   │   └── upload.py
│   └── main.py
├── tests/
├── alembic/
├── requirements.txt
├── Dockerfile
└── docker-compose.yml
```

## Setup

### Prerequisites

- Python 3.11+
- Supabase account (free tier available)
- (Optional) AWS S3 account for image storage

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up Supabase configuration:
```bash
# Quick setup with script
python scripts/setup_supabase.py

# Or manually:
cp .env.example .env
# Edit .env and replace [YOUR-PASSWORD] with your Supabase database password
```

5. Run database migrations:
```bash
alembic upgrade head
```

6. Run the application:
```bash
uvicorn app.main:app --reload
```

The API will be available at `http://localhost:8000`

### Docker Setup

1. Build and run with Docker Compose:
```bash
docker-compose up --build
```

This will start both the PostgreSQL database and the FastAPI application.

## API Documentation

Once the server is running, you can access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register a new user
- `POST /api/v1/auth/login` - Login user
- `GET /api/v1/auth/me` - Get current user info

### Users
- `GET /api/v1/users/{user_id}` - Get user profile
- `PATCH /api/v1/users/{user_id}` - Update user profile
- `POST /api/v1/users/{user_id}/follow` - Follow/unfollow user

### Pins
- `POST /api/v1/pins/` - Create a pin
- `GET /api/v1/pins/` - Get pins list
- `GET /api/v1/pins/{pin_id}` - Get pin details
- `PATCH /api/v1/pins/{pin_id}` - Update pin
- `DELETE /api/v1/pins/{pin_id}` - Delete pin
- `POST /api/v1/pins/{pin_id}/like` - Like/unlike pin
- `POST /api/v1/pins/{pin_id}/comments` - Add comment
- `GET /api/v1/pins/{pin_id}/comments` - Get comments

### Boards
- `POST /api/v1/boards/` - Create a board
- `GET /api/v1/boards/user/{user_id}` - Get user's boards
- `GET /api/v1/boards/{board_id}` - Get board details
- `PATCH /api/v1/boards/{board_id}` - Update board
- `DELETE /api/v1/boards/{board_id}` - Delete board
- `POST /api/v1/boards/{board_id}/pins` - Add pin to board
- `DELETE /api/v1/boards/{board_id}/pins` - Remove pin from board

### Upload
- `POST /api/v1/upload/image` - Upload image

## Testing

Run tests with pytest:

```bash
pytest
```

Run tests with coverage:

```bash
pytest --cov=app
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://postgres:password@localhost:5432/pinterest_clone` |
| `SECRET_KEY` | JWT secret key | `your-super-secret-key-change-this-in-production` |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | JWT token expiration | `30` |
| `AWS_ACCESS_KEY_ID` | AWS access key for S3 | - |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key for S3 | - |
| `AWS_BUCKET_NAME` | S3 bucket name | - |
| `AWS_REGION` | AWS region | `us-east-1` |
| `DEBUG` | Debug mode | `True` |

## Deployment

### Railway

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on push

### Render

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Set build command: `pip install -r requirements.txt`
4. Set start command: `uvicorn app.main:app --host 0.0.0.0 --port $PORT`
5. Set environment variables

### AWS EC2

1. Launch an EC2 instance
2. Install Docker and Docker Compose
3. Clone the repository
4. Run with Docker Compose:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is licensed under the MIT License.
