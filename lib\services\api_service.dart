import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const _storage = FlutterSecureStorage();
  
  // Auth endpoints
  static Future<Map<String, dynamic>> register({
    required String username,
    required String email,
    required String displayName,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'username': username,
        'email': email,
        'display_name': displayName,
        'password': password,
      }),
    );
    
    return _handleResponse(response);
  }
  
  static Future<Map<String, dynamic>> login({
    required String username,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login-json'),
      headers: {'Content-Type': 'application/json'},
      body: json<PERSON>ncode({
        'username': username,
        'password': password,
      }),
    );
    
    final result = _handleResponse(response);
    
    // Store token if login successful
    if (result['access_token'] != null) {
      await _storage.write(key: 'access_token', value: result['access_token']);
    }
    
    return result;
  }
  
  static Future<Map<String, dynamic>> getCurrentUser() async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) throw Exception('No token found');
    
    final response = await http.get(
      Uri.parse('$baseUrl/auth/me'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );
    
    return _handleResponse(response);
  }
  
  // Pin endpoints
  static Future<Map<String, dynamic>> getPins({
    int skip = 0,
    int limit = 20,
    int? userId,
  }) async {
    final token = await _storage.read(key: 'access_token');
    
    var url = '$baseUrl/pins/?skip=$skip&limit=$limit';
    if (userId != null) {
      url += '&user_id=$userId';
    }
    
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );
    
    return _handleResponse(response);
  }
  
  static Future<Map<String, dynamic>> createPin({
    required String title,
    required String description,
    required String imageUrl,
    double aspectRatio = 1.0,
    String? destinationUrl,
    bool isPublic = true,
  }) async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) throw Exception('No token found');
    
    final response = await http.post(
      Uri.parse('$baseUrl/pins/'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'title': title,
        'description': description,
        'image_url': imageUrl,
        'aspect_ratio': aspectRatio,
        'destination_url': destinationUrl,
        'is_public': isPublic,
      }),
    );
    
    return _handleResponse(response);
  }
  
  static Future<Map<String, dynamic>> likePin(int pinId) async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) throw Exception('No token found');
    
    final response = await http.post(
      Uri.parse('$baseUrl/pins/$pinId/like'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );
    
    return _handleResponse(response);
  }
  
  // Board endpoints
  static Future<Map<String, dynamic>> getUserBoards(int userId) async {
    final token = await _storage.read(key: 'access_token');
    
    final response = await http.get(
      Uri.parse('$baseUrl/boards/user/$userId'),
      headers: {
        'Content-Type': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );
    
    return _handleResponse(response);
  }
  
  static Future<Map<String, dynamic>> createBoard({
    required String name,
    String? description,
    bool isSecret = false,
  }) async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) throw Exception('No token found');
    
    final response = await http.post(
      Uri.parse('$baseUrl/boards/'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'name': name,
        'description': description,
        'is_secret': isSecret,
      }),
    );
    
    return _handleResponse(response);
  }
  
  // User endpoints
  static Future<Map<String, dynamic>> getUserProfile(String username) async {
    final token = await _storage.read(key: 'access_token');
    
    final response = await http.get(
      Uri.parse('$baseUrl/users/username/$username'),
      headers: {
        'Content-Type': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );
    
    return _handleResponse(response);
  }
  
  static Future<Map<String, dynamic>> followUser(int userId) async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) throw Exception('No token found');
    
    final response = await http.post(
      Uri.parse('$baseUrl/users/$userId/follow'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );
    
    return _handleResponse(response);
  }
  
  // Image upload
  static Future<Map<String, dynamic>> uploadImage(String filePath) async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) throw Exception('No token found');
    
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/upload/image'),
    );
    
    request.headers['Authorization'] = 'Bearer $token';
    request.files.add(await http.MultipartFile.fromPath('file', filePath));
    
    final streamedResponse = await request.send();
    final response = await http.Response.fromStream(streamedResponse);
    
    return _handleResponse(response);
  }
  
  // Utility methods
  static Future<void> logout() async {
    await _storage.delete(key: 'access_token');
  }
  
  static Future<bool> isLoggedIn() async {
    final token = await _storage.read(key: 'access_token');
    return token != null;
  }
  
  static Map<String, dynamic> _handleResponse(http.Response response) {
    final data = jsonDecode(response.body);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw Exception(data['detail'] ?? 'API Error: ${response.statusCode}');
    }
  }
}
