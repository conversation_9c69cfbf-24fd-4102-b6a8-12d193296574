from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.models import Comment, User
from app.schemas.pin import CommentCreate, Comment as CommentSchema

class CommentService:
    @staticmethod
    def create_comment(db: Session, comment_create: CommentCreate, user: User) -> CommentSchema:
        """Create a new comment"""
        db_comment = Comment(
            text=comment_create.text,
            pin_id=comment_create.pin_id,
            parent_id=comment_create.parent_id,
            user_id=user.id
        )
        
        db.add(db_comment)
        db.commit()
        db.refresh(db_comment)
        
        return CommentSchema(
            id=db_comment.id,
            text=db_comment.text,
            user_id=db_comment.user_id,
            pin_id=db_comment.pin_id,
            parent_id=db_comment.parent_id,
            created_at=db_comment.created_at,
            updated_at=db_comment.updated_at,
            user=db_comment.user,
            replies=[]
        )

    @staticmethod
    def get_pin_comments(db: Session, pin_id: int, skip: int = 0, limit: int = 20) -> List[CommentSchema]:
        """Get comments for a pin"""
        # Get top-level comments (no parent)
        comments = db.query(Comment).filter(
            Comment.pin_id == pin_id,
            Comment.parent_id.is_(None)
        ).order_by(desc(Comment.created_at)).offset(skip).limit(limit).all()
        
        result = []
        for comment in comments:
            # Get replies for each comment
            replies = db.query(Comment).filter(
                Comment.parent_id == comment.id
            ).order_by(Comment.created_at).all()
            
            reply_schemas = [
                CommentSchema(
                    id=reply.id,
                    text=reply.text,
                    user_id=reply.user_id,
                    pin_id=reply.pin_id,
                    parent_id=reply.parent_id,
                    created_at=reply.created_at,
                    updated_at=reply.updated_at,
                    user=reply.user,
                    replies=[]
                ) for reply in replies
            ]
            
            comment_schema = CommentSchema(
                id=comment.id,
                text=comment.text,
                user_id=comment.user_id,
                pin_id=comment.pin_id,
                parent_id=comment.parent_id,
                created_at=comment.created_at,
                updated_at=comment.updated_at,
                user=comment.user,
                replies=reply_schemas
            )
            result.append(comment_schema)
        
        return result
