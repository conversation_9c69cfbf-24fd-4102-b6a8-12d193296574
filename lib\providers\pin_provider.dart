import 'package:flutter/material.dart';
import '../models/pin.dart';

class PinProvider extends ChangeNotifier {
  List<Pin> _pins = [];
  List<User> _users = [];
  List<Board> _boards = [];
  bool _isLoading = false;

  List<Pin> get pins => _pins;
  List<User> get users => _users;
  List<Board> get boards => _boards;
  bool get isLoading => _isLoading;

  PinProvider() {
    _loadSampleData();
  }

  void _loadSampleData() {
    // Sample users
    _users = [
      User(
        id: '1',
        username: 'sarah_designs',
        displayName: '<PERSON>',
        avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
        bio: 'Interior designer & home decor enthusiast',
        followerCount: 12500,
        followingCount: 890,
      ),
      User(
        id: '2',
        username: 'foodie_mike',
        displayName: '<PERSON>',
        avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        bio: 'Chef & food photographer',
        followerCount: 8900,
        followingCount: 1200,
      ),
      User(
        id: '3',
        username: 'travel_emma',
        displayName: 'Emma Wilson',
        avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        bio: 'Travel blogger & photographer',
        followerCount: 25000,
        followingCount: 500,
      ),
    ];

    // Sample pins with various aspect ratios
    _pins = [
      Pin(
        id: '1',
        imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=600',
        title: 'Modern Living Room Design',
        description: 'Minimalist living room with neutral colors and natural lighting',
        user: _users[0],
        aspectRatio: 0.67,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        saveCount: 1250,
      ),
      Pin(
        id: '2',
        imageUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=500',
        title: 'Delicious Pasta Recipe',
        description: 'Creamy mushroom pasta with fresh herbs',
        user: _users[1],
        aspectRatio: 0.8,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        saveCount: 890,
      ),
      Pin(
        id: '3',
        imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=700',
        title: 'Mountain Landscape',
        description: 'Breathtaking view of snow-capped mountains',
        user: _users[2],
        aspectRatio: 0.57,
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        saveCount: 2100,
      ),
      Pin(
        id: '4',
        imageUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=400',
        title: 'Bedroom Decor Ideas',
        description: 'Cozy bedroom with warm lighting and plants',
        user: _users[0],
        aspectRatio: 1.0,
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        saveCount: 750,
      ),
      Pin(
        id: '5',
        imageUrl: 'https://images.unsplash.com/photo-**********-a2132b4ba21d?w=400&h=600',
        title: 'Healthy Breakfast Bowl',
        description: 'Acai bowl with fresh fruits and granola',
        user: _users[1],
        aspectRatio: 0.67,
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        saveCount: 1100,
      ),
      Pin(
        id: '6',
        imageUrl: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=800',
        title: 'Ocean Sunset',
        description: 'Beautiful sunset over the ocean waves',
        user: _users[2],
        aspectRatio: 0.5,
        createdAt: DateTime.now().subtract(const Duration(hours: 4)),
        saveCount: 3200,
      ),
    ];

    // Add more pins for variety
    _addMoreSamplePins();
  }

  void _addMoreSamplePins() {
    final additionalPins = [
      Pin(
        id: '7',
        imageUrl: 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=500',
        title: 'Kitchen Organization',
        description: 'Smart storage solutions for small kitchens',
        user: _users[0],
        aspectRatio: 0.8,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        saveCount: 680,
      ),
      Pin(
        id: '8',
        imageUrl: 'https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?w=400&h=600',
        title: 'Homemade Pizza',
        description: 'Wood-fired pizza with fresh mozzarella',
        user: _users[1],
        aspectRatio: 0.67,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        saveCount: 920,
      ),
      Pin(
        id: '9',
        imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=700',
        title: 'City Architecture',
        description: 'Modern skyscrapers against blue sky',
        user: _users[2],
        aspectRatio: 0.57,
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        saveCount: 1500,
      ),
    ];

    _pins.addAll(additionalPins);
  }

  Pin? getPinById(String id) {
    try {
      return _pins.firstWhere((pin) => pin.id == id);
    } catch (e) {
      return null;
    }
  }

  User? getUserByUsername(String username) {
    try {
      return _users.firstWhere((user) => user.username == username);
    } catch (e) {
      return null;
    }
  }

  List<Pin> getPinsByUser(String userId) {
    return _pins.where((pin) => pin.user.id == userId).toList();
  }

  List<Pin> getRelatedPins(String pinId) {
    // Simple related pins logic - return random pins excluding the current one
    return _pins.where((pin) => pin.id != pinId).take(6).toList();
  }

  void savePin(String pinId) {
    // In a real app, this would save to backend
    notifyListeners();
  }

  void followUser(String userId) {
    // In a real app, this would update backend
    notifyListeners();
  }
}
