import 'package:flutter/material.dart';
import '../models/pin.dart';
import '../services/api_service.dart';

class PinProvider extends ChangeNotifier {
  List<Pin> _pins = [];
  List<User> _users = [];
  List<Board> _boards = [];
  bool _isLoading = false;

  List<Pin> get pins => _pins;
  List<User> get users => _users;
  List<Board> get boards => _boards;
  bool get isLoading => _isLoading;

  PinProvider() {
    _loadSampleData();
    loadPinsFromAPI();
  }

  Future<void> loadPinsFromAPI() async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await ApiService.getPins();
      final pinsData = response['pins'] as List;

      // Convert API data to Pin objects
      List<Pin> apiPins = pinsData.map((pinData) => Pin(
        id: pinData['id'].toString(),
        imageUrl: pinData['image_url'],
        title: pinData['title'] ?? '',
        description: pinData['description'] ?? '',
        user: User(
          id: pinData['user']['id'].toString(),
          username: pinData['user']['username'],
          displayName: pinData['user']['display_name'],
          avatarUrl: pinData['user']['avatar_url'] ?? 'https://picsum.photos/150/150?random=1',
        ),
        aspectRatio: pinData['aspect_ratio']?.toDouble() ?? 1.0,
        createdAt: DateTime.parse(pinData['created_at']),
        saveCount: pinData['like_count'] ?? 0,
      )).toList();

      // Combine API pins with sample data for better display
      _pins = [...apiPins, ..._pins];

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error loading pins from API: $e');
      // Keep sample data if API fails
    }
  }

  void _loadSampleData() {
    // Sample users
    _users = [
      User(
        id: '1',
        username: 'sarah_designs',
        displayName: 'Sarah Johnson',
        avatarUrl: 'https://picsum.photos/150/150?random=1',
        bio: 'Interior designer & home decor enthusiast',
        followerCount: 12500,
        followingCount: 890,
      ),
      User(
        id: '2',
        username: 'foodie_mike',
        displayName: 'Mike Chen',
        avatarUrl: 'https://picsum.photos/150/150?random=2',
        bio: 'Chef & food photographer',
        followerCount: 8900,
        followingCount: 1200,
      ),
      User(
        id: '3',
        username: 'travel_emma',
        displayName: 'Emma Wilson',
        avatarUrl: 'https://picsum.photos/150/150?random=3',
        bio: 'Travel blogger & photographer',
        followerCount: 25000,
        followingCount: 500,
      ),
    ];

    // Sample pins with various aspect ratios (Pinterest-style)
    _pins = [
      Pin(
        id: '1',
        imageUrl: 'https://picsum.photos/400/600?random=10',
        title: 'Modern Living Room Design',
        description: 'Minimalist living room with neutral colors and natural lighting',
        user: _users[0],
        aspectRatio: 0.75, // 3:4 ratio
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        saveCount: 1250,
      ),
      Pin(
        id: '2',
        imageUrl: 'https://picsum.photos/400/500?random=11',
        title: 'Delicious Pasta Recipe',
        description: 'Creamy mushroom pasta with fresh herbs',
        user: _users[1],
        aspectRatio: 0.8, // 4:5 ratio
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        saveCount: 890,
      ),
      Pin(
        id: '3',
        imageUrl: 'https://picsum.photos/400/700?random=12',
        title: 'Mountain Landscape',
        description: 'Breathtaking view of snow-capped mountains',
        user: _users[2],
        aspectRatio: 0.6, // 3:5 ratio
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        saveCount: 2100,
      ),
      Pin(
        id: '4',
        imageUrl: 'https://picsum.photos/400/400?random=13',
        title: 'Bedroom Decor Ideas',
        description: 'Cozy bedroom with warm lighting and plants',
        user: _users[0],
        aspectRatio: 1.0, // 1:1 ratio
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        saveCount: 750,
      ),
      Pin(
        id: '5',
        imageUrl: 'https://picsum.photos/400/600?random=14',
        title: 'Healthy Breakfast Bowl',
        description: 'Acai bowl with fresh fruits and granola',
        user: _users[1],
        aspectRatio: 0.67, // 2:3 ratio
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        saveCount: 1100,
      ),
      Pin(
        id: '6',
        imageUrl: 'https://picsum.photos/400/800?random=15',
        title: 'Ocean Sunset',
        description: 'Beautiful sunset over the ocean waves',
        user: _users[2],
        aspectRatio: 0.5, // 1:2 ratio
        createdAt: DateTime.now().subtract(const Duration(hours: 4)),
        saveCount: 3200,
      ),
    ];

    // Add more pins for variety
    _addMoreSamplePins();
  }

  void _addMoreSamplePins() {
    final additionalPins = [
      Pin(
        id: '7',
        imageUrl: 'https://picsum.photos/400/500?random=16',
        title: 'Kitchen Organization',
        description: 'Smart storage solutions for small kitchens',
        user: _users[0],
        aspectRatio: 0.8,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        saveCount: 680,
      ),
      Pin(
        id: '8',
        imageUrl: 'https://picsum.photos/400/600?random=17',
        title: 'Homemade Pizza',
        description: 'Wood-fired pizza with fresh mozzarella',
        user: _users[1],
        aspectRatio: 0.67,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        saveCount: 920,
      ),
      Pin(
        id: '9',
        imageUrl: 'https://picsum.photos/400/800?random=18',
        title: 'Fashion Inspiration',
        description: 'Elegant outfit ideas for spring',
        user: _users[2],
        aspectRatio: 0.5,
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        saveCount: 1500,
      ),
      Pin(
        id: '10',
        imageUrl: 'https://picsum.photos/400/300?random=19',
        title: 'Workout Routine',
        description: 'Morning yoga poses for beginners',
        user: _users[0],
        aspectRatio: 1.33,
        createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
        saveCount: 450,
      ),
      Pin(
        id: '11',
        imageUrl: 'https://picsum.photos/400/600?random=20',
        title: 'Garden Design',
        description: 'Beautiful backyard landscaping ideas',
        user: _users[1],
        aspectRatio: 0.67,
        createdAt: DateTime.now().subtract(const Duration(minutes: 45)),
        saveCount: 890,
      ),
      Pin(
        id: '12',
        imageUrl: 'https://picsum.photos/400/500?random=21',
        title: 'Art Inspiration',
        description: 'Abstract painting techniques',
        user: _users[2],
        aspectRatio: 0.8,
        createdAt: DateTime.now().subtract(const Duration(minutes: 20)),
        saveCount: 1200,
      ),
    ];

    _pins.addAll(additionalPins);
  }

  Pin? getPinById(String id) {
    try {
      return _pins.firstWhere((pin) => pin.id == id);
    } catch (e) {
      return null;
    }
  }

  User? getUserByUsername(String username) {
    try {
      return _users.firstWhere((user) => user.username == username);
    } catch (e) {
      return null;
    }
  }

  List<Pin> getPinsByUser(String userId) {
    return _pins.where((pin) => pin.user.id == userId).toList();
  }

  List<Pin> getRelatedPins(String pinId) {
    // Simple related pins logic - return random pins excluding the current one
    return _pins.where((pin) => pin.id != pinId).take(6).toList();
  }

  void savePin(String pinId) {
    // In a real app, this would save to backend
    notifyListeners();
  }

  void followUser(String userId) {
    // In a real app, this would update backend
    notifyListeners();
  }
}
