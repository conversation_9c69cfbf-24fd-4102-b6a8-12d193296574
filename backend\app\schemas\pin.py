from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel

from app.schemas.user import UserInDB

# Pin Schemas
class PinBase(BaseModel):
    title: str
    description: Optional[str] = None
    destination_url: Optional[str] = None
    is_public: bool = True

class PinCreate(PinBase):
    image_url: str
    aspect_ratio: Optional[float] = 1.0

class PinUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    destination_url: Optional[str] = None
    is_public: Optional[bool] = None

class PinInDB(PinBase):
    id: int
    image_url: str
    aspect_ratio: float
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Pin(PinInDB):
    user: UserInDB
    like_count: int = 0
    comment_count: int = 0
    is_liked: bool = False

class PinList(BaseModel):
    pins: List[Pin]
    total: int
    page: int
    size: int
    has_next: bool

# Comment Schemas
class CommentBase(BaseModel):
    text: str

class CommentCreate(CommentBase):
    pin_id: int
    parent_id: Optional[int] = None

class CommentUpdate(BaseModel):
    text: str

class CommentInDB(CommentBase):
    id: int
    user_id: int
    pin_id: int
    parent_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Comment(CommentInDB):
    user: UserInDB
    replies: List["Comment"] = []

# Update forward references
Comment.model_rebuild()
