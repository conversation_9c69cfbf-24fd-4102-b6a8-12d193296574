from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user, get_optional_current_user
from app.schemas.pin import PinC<PERSON>, PinUp<PERSON>, Pin, Pin<PERSON><PERSON>, CommentCreate, Comment
from app.services.pin import PinService
from app.services.comment import CommentService

router = APIRouter()

@router.post("/", response_model=Pin, status_code=status.HTTP_201_CREATED)
async def create_pin(
    pin_create: PinCreate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new pin"""
    db_pin = PinService.create_pin(db, pin_create, current_user)
    return PinService._pin_to_schema(db_pin, current_user, db)

@router.get("/", response_model=PinList)
async def get_pins(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    user_id: Optional[int] = Query(None),
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get list of pins with pagination"""
    return PinService.get_pins(db, skip, limit, user_id, current_user)

@router.get("/{pin_id}", response_model=Pin)
async def get_pin(
    pin_id: int,
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific pin"""
    pin = PinService.get_pin(db, pin_id, current_user)
    if not pin:
        raise HTTPException(status_code=404, detail="Pin not found")
    
    return PinService._pin_to_schema(pin, current_user, db)

@router.patch("/{pin_id}", response_model=Pin)
async def update_pin(
    pin_id: int,
    pin_update: PinUpdate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a pin"""
    pin = PinService.update_pin(db, pin_id, pin_update, current_user)
    if not pin:
        raise HTTPException(status_code=404, detail="Pin not found or not authorized")
    
    return PinService._pin_to_schema(pin, current_user, db)

@router.delete("/{pin_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_pin(
    pin_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a pin"""
    success = PinService.delete_pin(db, pin_id, current_user)
    if not success:
        raise HTTPException(status_code=404, detail="Pin not found or not authorized")

@router.post("/{pin_id}/like")
async def like_pin(
    pin_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Like/unlike a pin"""
    is_liked = PinService.like_pin(db, pin_id, current_user)
    return {"liked": is_liked}

@router.post("/{pin_id}/comments", response_model=Comment, status_code=status.HTTP_201_CREATED)
async def create_comment(
    pin_id: int,
    comment_create: CommentCreate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a comment on a pin"""
    # Verify pin exists
    pin = PinService.get_pin(db, pin_id, current_user)
    if not pin:
        raise HTTPException(status_code=404, detail="Pin not found")
    
    comment_create.pin_id = pin_id
    return CommentService.create_comment(db, comment_create, current_user)

@router.get("/{pin_id}/comments")
async def get_pin_comments(
    pin_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get comments for a pin"""
    # Verify pin exists
    pin = PinService.get_pin(db, pin_id, current_user)
    if not pin:
        raise HTTPException(status_code=404, detail="Pin not found")
    
    return CommentService.get_pin_comments(db, pin_id, skip, limit)
