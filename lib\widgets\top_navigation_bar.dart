import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../theme/app_theme.dart';

class TopNavigationBar extends StatefulWidget {
  const TopNavigationBar({super.key});

  @override
  State<TopNavigationBar> createState() => _TopNavigationBarState();
}

class _TopNavigationBarState extends State<TopNavigationBar> {
  final TextEditingController _searchController = TextEditingController();
  int _selectedTabIndex = 0;

  final List<String> _navigationTabs = [
    'Home',
    'Today',
    'Explore',
    'Following',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              // Top row with logo, search, and profile icons
              Row(
                children: [
                  // Pinterest Logo
                  _buildLogo(),
                  
                  const SizedBox(width: 16),
                  
                  // Search Bar
                  Expanded(child: _buildSearchBar()),
                  
                  const SizedBox(width: 16),
                  
                  // Right side icons
                  _buildRightIcons(),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Navigation tabs
              _buildNavigationTabs(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return GestureDetector(
      onTap: () => context.go('/'),
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppTheme.pinterestRed,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.push_pin,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              'Pinterest',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.pinterestRed,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: AppTheme.backgroundSecondary,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'Search for ideas',
          prefixIcon: Icon(
            Icons.search,
            color: AppTheme.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        ),
        onSubmitted: (value) {
          // Handle search
          if (value.isNotEmpty) {
            // In a real app, navigate to search results
            print('Searching for: $value');
          }
        },
      ),
    );
  }

  Widget _buildRightIcons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Notifications
        IconButton(
          onPressed: () {
            // Handle notifications
          },
          icon: const Icon(Icons.notifications_outlined),
          tooltip: 'Notifications',
        ),
        
        // Messages
        IconButton(
          onPressed: () {
            // Handle messages
          },
          icon: const Icon(Icons.chat_bubble_outline),
          tooltip: 'Messages',
        ),
        
        // Profile
        GestureDetector(
          onTap: () => context.go('/profile/current_user'),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppTheme.borderColor),
              image: const DecorationImage(
                image: NetworkImage(
                  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
                ),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationTabs() {
    return Row(
      children: _navigationTabs.asMap().entries.map((entry) {
        final index = entry.key;
        final tab = entry.value;
        final isSelected = index == _selectedTabIndex;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedTabIndex = index;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.textPrimary : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              tab,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
