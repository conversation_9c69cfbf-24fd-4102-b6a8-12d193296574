import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../theme/app_theme.dart';

class TopNavigationBar extends StatefulWidget {
  const TopNavigationBar({super.key});

  @override
  State<TopNavigationBar> createState() => _TopNavigationBarState();
}

class _TopNavigationBarState extends State<TopNavigationBar> {
  final TextEditingController _searchController = TextEditingController();
  int _selectedTabIndex = 0;

  final List<String> _navigationTabs = [
    'Home',
    'Today',
    'Explore',
    'Following',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Pinterest Logo
              _buildLogo(),

              const SizedBox(width: 24),

              // Navigation tabs (Home, Today, etc.)
              _buildNavigationTabs(),

              const SizedBox(width: 24),

              // Search Bar
              Expanded(child: _buildSearchBar()),

              const SizedBox(width: 24),

              // Right side icons
              _buildRightIcons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return GestureDetector(
      onTap: () => context.go('/'),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: _selectedTabIndex == -1 ? AppTheme.textPrimary : Colors.transparent,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                color: AppTheme.pinterestRed,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.push_pin,
                color: Colors.white,
                size: 14,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Pinterest',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: _selectedTabIndex == -1 ? AppTheme.backgroundColor : AppTheme.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 48,
      constraints: const BoxConstraints(maxWidth: 600),
      decoration: BoxDecoration(
        color: AppTheme.backgroundSecondary,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: AppTheme.textPrimary),
        decoration: const InputDecoration(
          hintText: 'Search for ideas',
          hintStyle: TextStyle(color: AppTheme.textSecondary),
          prefixIcon: Icon(
            Icons.search,
            color: AppTheme.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        ),
        onSubmitted: (value) {
          // Handle search
          if (value.isNotEmpty) {
            // In a real app, navigate to search results
            debugPrint('Searching for: $value');
          }
        },
      ),
    );
  }

  Widget _buildRightIcons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Notifications
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(24),
          ),
          child: IconButton(
            onPressed: () {
              // Handle notifications
            },
            icon: const Icon(
              Icons.notifications_outlined,
              color: AppTheme.textPrimary,
              size: 24,
            ),
            tooltip: 'Notifications',
          ),
        ),

        // Messages
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(24),
          ),
          child: IconButton(
            onPressed: () {
              // Handle messages
            },
            icon: const Icon(
              Icons.chat_bubble_outline,
              color: AppTheme.textPrimary,
              size: 24,
            ),
            tooltip: 'Messages',
          ),
        ),

        // Login/Profile
        GestureDetector(
          onTap: () => context.go('/login'),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.pinterestRed,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              'Login',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationTabs() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _navigationTabs.asMap().entries.map((entry) {
        final index = entry.key;
        final tab = entry.value;
        final isSelected = index == _selectedTabIndex;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedTabIndex = index;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.textPrimary : Colors.transparent,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Text(
              tab,
              style: TextStyle(
                color: isSelected ? AppTheme.backgroundColor : AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
