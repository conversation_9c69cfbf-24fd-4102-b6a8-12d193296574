from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, pins, boards, upload

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(pins.router, prefix="/pins", tags=["pins"])
api_router.include_router(boards.router, prefix="/boards", tags=["boards"])
api_router.include_router(upload.router, prefix="/upload", tags=["upload"])
