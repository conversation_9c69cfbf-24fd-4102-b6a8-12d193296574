import 'package:flutter/material.dart';
import '../models/pin.dart';
import '../services/api_service.dart';

class BoardProvider extends ChangeNotifier {
  List<Board> _boards = [];
  bool _isLoading = false;

  List<Board> get boards => _boards;
  bool get isLoading => _isLoading;

  BoardProvider() {
    _loadSampleBoards();
  }

  void _loadSampleBoards() {
    // Sample boards for display
    _boards = [
      Board(
        id: '1',
        name: 'Home Decor',
        description: 'Beautiful home decoration ideas',
        pins: [],
        owner: User(
          id: '1',
          username: 'sarah_designs',
          displayName: '<PERSON>',
          avatarUrl: 'https://picsum.photos/150/150?random=1',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Board(
        id: '2',
        name: 'Recipes',
        description: 'Delicious recipes to try',
        pins: [],
        owner: User(
          id: '2',
          username: 'foodie_mike',
          displayName: '<PERSON>',
          avatarUrl: 'https://picsum.photos/150/150?random=2',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Board(
        id: '3',
        name: 'Travel',
        description: 'Amazing places to visit',
        pins: [],
        owner: User(
          id: '3',
          username: 'travel_emma',
          displayName: 'Emma Wilson',
          avatarUrl: 'https://picsum.photos/150/150?random=3',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ];
  }

  Future<void> loadUserBoards(String userId) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await ApiService.getUserBoards(int.parse(userId));
      final boardsData = response['boards'] as List;

      List<Board> apiBoards = boardsData.map((boardData) => Board(
        id: boardData['id'].toString(),
        name: boardData['name'],
        description: boardData['description'] ?? '',
        pins: [], // Will be loaded separately if needed
        owner: User(
          id: boardData['user']['id'].toString(),
          username: boardData['user']['username'],
          displayName: boardData['user']['display_name'],
          avatarUrl: boardData['user']['avatar_url'] ?? 'https://picsum.photos/150/150?random=1',
        ),
        createdAt: DateTime.parse(boardData['created_at']),
      )).toList();

      // Combine API boards with sample data
      _boards = [...apiBoards, ..._boards];

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error loading boards from API: $e');
    }
  }

  Future<bool> createBoard({
    required String name,
    String? description,
    bool isSecret = false,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await ApiService.createBoard(
        name: name,
        description: description,
        isSecret: isSecret,
      );

      // Add the new board to the list
      final newBoard = Board(
        id: response['id'].toString(),
        name: response['name'],
        description: response['description'] ?? '',
        pins: [],
        owner: User(
          id: response['user']['id'].toString(),
          username: response['user']['username'],
          displayName: response['user']['display_name'],
          avatarUrl: response['user']['avatar_url'] ?? 'https://picsum.photos/150/150?random=1',
        ),
        createdAt: DateTime.parse(response['created_at']),
      );

      _boards.insert(0, newBoard);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error creating board: $e');
      return false;
    }
  }

  Board? getBoardById(String id) {
    try {
      return _boards.firstWhere((board) => board.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Board> getBoardsByUser(String userId) {
    return _boards.where((board) => board.owner.id == userId).toList();
  }
}
