from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException, status

from app.models.models import User, user_follows
from app.schemas.user import UserUpdate, User as UserSchema, UserProfile

class UserService:
    @staticmethod
    def get_user_by_id(db: Session, user_id: int, current_user: Optional[User] = None) -> Optional[UserProfile]:
        """Get user by ID"""
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None
        
        return UserService._user_to_profile_schema(user, current_user, db)

    @staticmethod
    def get_user_by_username(db: Session, username: str, current_user: Optional[User] = None) -> Optional[UserProfile]:
        """Get user by username"""
        user = db.query(User).filter(User.username == username).first()
        if not user:
            return None
        
        return UserService._user_to_profile_schema(user, current_user, db)

    @staticmethod
    def update_user(db: Session, user_id: int, user_update: UserUpdate, current_user: User) -> Optional[UserSchema]:
        """Update user profile"""
        # Users can only update their own profile
        if current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this profile"
            )
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None
        
        update_data = user_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        db.commit()
        db.refresh(user)
        
        return UserService._user_to_schema(user, db)

    @staticmethod
    def follow_user(db: Session, user_id: int, current_user: User) -> bool:
        """Follow/unfollow a user"""
        if current_user.id == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot follow yourself"
            )
        
        # Check if target user exists
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if already following
        existing_follow = db.query(user_follows).filter(
            user_follows.c.follower_id == current_user.id,
            user_follows.c.following_id == user_id
        ).first()
        
        if existing_follow:
            # Unfollow
            db.execute(
                user_follows.delete().where(
                    user_follows.c.follower_id == current_user.id,
                    user_follows.c.following_id == user_id
                )
            )
            db.commit()
            return False
        else:
            # Follow
            db.execute(
                user_follows.insert().values(
                    follower_id=current_user.id,
                    following_id=user_id
                )
            )
            db.commit()
            return True

    @staticmethod
    def _user_to_schema(user: User, db: Session) -> UserSchema:
        """Convert User model to schema"""
        # Count relationships
        follower_count = db.query(func.count(user_follows.c.follower_id)).filter(
            user_follows.c.following_id == user.id
        ).scalar() or 0
        
        following_count = db.query(func.count(user_follows.c.following_id)).filter(
            user_follows.c.follower_id == user.id
        ).scalar() or 0
        
        pin_count = len(user.pins)
        board_count = len(user.boards)
        
        return UserSchema(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            bio=user.bio,
            website=user.website,
            avatar_url=user.avatar_url,
            cover_image_url=user.cover_image_url,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
            follower_count=follower_count,
            following_count=following_count,
            pin_count=pin_count,
            board_count=board_count
        )

    @staticmethod
    def _user_to_profile_schema(user: User, current_user: Optional[User], db: Session) -> UserProfile:
        """Convert User model to profile schema"""
        # Count relationships
        follower_count = db.query(func.count(user_follows.c.follower_id)).filter(
            user_follows.c.following_id == user.id
        ).scalar() or 0
        
        following_count = db.query(func.count(user_follows.c.following_id)).filter(
            user_follows.c.follower_id == user.id
        ).scalar() or 0
        
        pin_count = len(user.pins)
        board_count = len(user.boards)
        
        # Check if current user is following this user
        is_following = False
        if current_user and current_user.id != user.id:
            is_following = db.query(user_follows).filter(
                user_follows.c.follower_id == current_user.id,
                user_follows.c.following_id == user.id
            ).first() is not None
        
        return UserProfile(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            bio=user.bio,
            website=user.website,
            avatar_url=user.avatar_url,
            cover_image_url=user.cover_image_url,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
            follower_count=follower_count,
            following_count=following_count,
            pin_count=pin_count,
            board_count=board_count,
            is_following=is_following
        )
