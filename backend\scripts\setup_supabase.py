#!/usr/bin/env python3
"""
Script to help setup Supabase configuration
"""

import os
import sys
from pathlib import Path

def setup_supabase():
    """Setup Supabase configuration"""
    
    print("🚀 Pinterest Clone - Supabase Setup")
    print("=" * 50)
    
    # Get the backend directory
    backend_dir = Path(__file__).parent.parent
    env_example_path = backend_dir / ".env.example"
    env_path = backend_dir / ".env"
    alembic_ini_path = backend_dir / "alembic.ini"
    
    # Get database password from user
    print("\n📋 Please provide your Supabase database password:")
    print("   1. Go to https://krnaiyjplgzawfrbapji.supabase.co")
    print("   2. Navigate to Settings → Database")
    print("   3. Copy your database password")
    print()
    
    password = input("Enter your Supabase database password: ").strip()
    
    if not password:
        print("❌ Password cannot be empty!")
        sys.exit(1)
    
    # Create .env file from .env.example
    if env_example_path.exists():
        with open(env_example_path, 'r') as f:
            content = f.read()
        
        # Replace placeholder with actual password
        content = content.replace('[YOUR-PASSWORD]', password)
        
        with open(env_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Created .env file with Supabase configuration")
    else:
        print("❌ .env.example file not found!")
        sys.exit(1)
    
    # Update alembic.ini
    if alembic_ini_path.exists():
        with open(alembic_ini_path, 'r') as f:
            content = f.read()
        
        # Replace placeholder with actual password
        content = content.replace('[YOUR-PASSWORD]', password)
        
        with open(alembic_ini_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated alembic.ini with Supabase configuration")
    else:
        print("❌ alembic.ini file not found!")
        sys.exit(1)
    
    print("\n🎉 Supabase setup complete!")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Run migrations: alembic upgrade head")
    print("3. Create sample data: python scripts/create_sample_data.py")
    print("4. Start the server: python run.py")
    print("\nAPI will be available at: http://localhost:8000")
    print("API docs will be available at: http://localhost:8000/docs")

if __name__ == "__main__":
    setup_supabase()
