import os
import uuid
import io
from typing import Optional
from fastapi import UploadFile, HTTPException, status
from PIL import Image
import boto3
from botocore.exceptions import ClientError

from app.core.config import settings

class UploadService:
    def __init__(self):
        self.s3_client = None
        if settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION
            )

    async def upload_image(self, file: UploadFile, user_id: int) -> dict:
        """Upload image and return URL and metadata"""
        # Validate file
        self._validate_image(file)
        
        # Generate unique filename
        file_extension = file.filename.split('.')[-1].lower()
        unique_filename = f"{user_id}_{uuid.uuid4().hex}.{file_extension}"
        
        # Read file content
        content = await file.read()
        
        # Get image dimensions and aspect ratio
        image_info = self._get_image_info(content)
        
        # Upload to S3 or local storage
        if self.s3_client and settings.AWS_BUCKET_NAME:
            image_url = await self._upload_to_s3(content, unique_filename)
        else:
            image_url = await self._upload_to_local(content, unique_filename)
        
        return {
            "image_url": image_url,
            "aspect_ratio": image_info["aspect_ratio"],
            "width": image_info["width"],
            "height": image_info["height"]
        }

    def _validate_image(self, file: UploadFile):
        """Validate uploaded image file"""
        # Check file size
        if file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size too large. Maximum size is {settings.MAX_FILE_SIZE / (1024*1024):.1f}MB"
            )
        
        # Check file type
        if file.content_type not in settings.ALLOWED_IMAGE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed types: {', '.join(settings.ALLOWED_IMAGE_TYPES)}"
            )

    def _get_image_info(self, content: bytes) -> dict:
        """Get image dimensions and aspect ratio"""
        try:
            with Image.open(io.BytesIO(content)) as img:
                width, height = img.size
                aspect_ratio = width / height
                return {
                    "width": width,
                    "height": height,
                    "aspect_ratio": aspect_ratio
                }
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid image file"
            )

    async def _upload_to_s3(self, content: bytes, filename: str) -> str:
        """Upload image to AWS S3"""
        try:
            self.s3_client.put_object(
                Bucket=settings.AWS_BUCKET_NAME,
                Key=f"pins/{filename}",
                Body=content,
                ContentType="image/jpeg"
            )
            
            # Return S3 URL
            return f"https://{settings.AWS_BUCKET_NAME}.s3.{settings.AWS_REGION}.amazonaws.com/pins/{filename}"
        
        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload image to S3"
            )

    async def _upload_to_local(self, content: bytes, filename: str) -> str:
        """Upload image to local storage"""
        try:
            # Create upload directory if it doesn't exist
            upload_dir = os.path.join(settings.UPLOAD_DIR, "pins")
            os.makedirs(upload_dir, exist_ok=True)
            
            # Save file
            file_path = os.path.join(upload_dir, filename)
            with open(file_path, "wb") as f:
                f.write(content)
            
            # Return local URL
            return f"/uploads/pins/{filename}"
        
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save image"
            )

    def delete_image(self, image_url: str) -> bool:
        """Delete image from storage"""
        try:
            if self.s3_client and settings.AWS_BUCKET_NAME and "s3.amazonaws.com" in image_url:
                # Extract key from S3 URL
                key = image_url.split(f"{settings.AWS_BUCKET_NAME}.s3.{settings.AWS_REGION}.amazonaws.com/")[1]
                self.s3_client.delete_object(Bucket=settings.AWS_BUCKET_NAME, Key=key)
            else:
                # Delete from local storage
                if image_url.startswith("/uploads/"):
                    file_path = image_url[1:]  # Remove leading slash
                    if os.path.exists(file_path):
                        os.remove(file_path)
            
            return True
        except Exception:
            return False


