from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user, get_optional_current_user
from app.schemas.user import UserUpdate, User, UserProfile
from app.services.user import UserService

router = APIRouter()

@router.get("/{user_id}", response_model=UserProfile)
async def get_user_by_id(
    user_id: int = Path(..., description="User ID"),
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get user profile by ID"""
    user = UserService.get_user_by_id(db, user_id, current_user)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

@router.get("/username/{username}", response_model=UserProfile)
async def get_user_by_username(
    username: str = Path(..., description="Username"),
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get user profile by username"""
    user = UserService.get_user_by_username(db, username, current_user)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

@router.patch("/{user_id}", response_model=User)
async def update_user(
    user_id: int = Path(..., description="User ID"),
    user_update: UserUpdate = ...,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    user = UserService.update_user(db, user_id, user_update, current_user)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

@router.post("/{user_id}/follow")
async def follow_user(
    user_id: int = Path(..., description="User ID to follow/unfollow"),
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Follow/unfollow a user"""
    is_following = UserService.follow_user(db, user_id, current_user)
    return {"following": is_following}
