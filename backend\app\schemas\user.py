from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr

# User Schemas
class UserBase(BaseModel):
    username: str
    email: EmailStr
    display_name: str
    bio: Optional[str] = None
    website: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    display_name: Optional[str] = None
    bio: Optional[str] = None
    website: Optional[str] = None
    avatar_url: Optional[str] = None
    cover_image_url: Optional[str] = None

class UserInDB(UserBase):
    id: int
    avatar_url: Optional[str] = None
    cover_image_url: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class User(UserInDB):
    follower_count: int = 0
    following_count: int = 0
    pin_count: int = 0
    board_count: int = 0

class UserProfile(User):
    is_following: bool = False

# Auth Schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class LoginRequest(BaseModel):
    username: str
    password: str
