from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.schemas.user import User<PERSON><PERSON>, User, Token, LoginRequest
from app.services.auth import AuthService

router = APIRouter()

@router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register(
    user_create: UserCreate,
    db: Session = Depends(get_db)
):
    """Register a new user"""
    user = AuthService.create_user(db, user_create)
    
    # Convert to response model
    return User(
        id=user.id,
        username=user.username,
        email=user.email,
        display_name=user.display_name,
        bio=user.bio,
        website=user.website,
        avatar_url=user.avatar_url,
        cover_image_url=user.cover_image_url,
        is_active=user.is_active,
        is_verified=user.is_verified,
        created_at=user.created_at,
        updated_at=user.updated_at,
        follower_count=0,
        following_count=0,
        pin_count=0,
        board_count=0
    )

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Login user with OAuth2 password flow"""
    return AuthService.login(db, form_data.username, form_data.password)

@router.post("/login-json", response_model=Token)
async def login_json(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """Login user with JSON payload"""
    return AuthService.login(db, login_data.username, login_data.password)

@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user information"""
    # Count relationships
    follower_count = len(current_user.followers)
    following_count = len(current_user.following)
    pin_count = len(current_user.pins)
    board_count = len(current_user.boards)
    
    return User(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        display_name=current_user.display_name,
        bio=current_user.bio,
        website=current_user.website,
        avatar_url=current_user.avatar_url,
        cover_image_url=current_user.cover_image_url,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        follower_count=follower_count,
        following_count=following_count,
        pin_count=pin_count,
        board_count=board_count
    )
