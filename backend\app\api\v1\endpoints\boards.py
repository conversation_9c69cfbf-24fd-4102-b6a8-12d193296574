from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user, get_optional_current_user
from app.schemas.board import (
    BoardCreate, 
    BoardUpdate, 
    Board, 
    BoardWithPins, 
    BoardList,
    AddPinToBoard,
    RemovePinFromBoard
)
from app.services.board import BoardService

router = APIRouter()

@router.post("/", response_model=Board, status_code=status.HTTP_201_CREATED)
async def create_board(
    board_create: BoardCreate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new board"""
    return BoardService.create_board(db, board_create, current_user)

@router.get("/user/{user_id}", response_model=BoardList)
async def get_user_boards(
    user_id: int = Path(..., description="User ID"),
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get all boards for a user"""
    return BoardService.get_user_boards(db, user_id, current_user)

@router.get("/{board_id}", response_model=BoardWithPins)
async def get_board(
    board_id: int = Path(..., description="Board ID"),
    current_user = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """Get a board with its pins"""
    board = BoardService.get_board(db, board_id, current_user)
    if not board:
        raise HTTPException(status_code=404, detail="Board not found")
    
    return board

@router.patch("/{board_id}", response_model=Board)
async def update_board(
    board_id: int = Path(..., description="Board ID"),
    board_update: BoardUpdate = ...,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a board"""
    board = BoardService.update_board(db, board_id, board_update, current_user)
    if not board:
        raise HTTPException(status_code=404, detail="Board not found or not authorized")
    
    return board

@router.delete("/{board_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_board(
    board_id: int = Path(..., description="Board ID"),
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a board"""
    success = BoardService.delete_board(db, board_id, current_user)
    if not success:
        raise HTTPException(status_code=404, detail="Board not found or not authorized")

@router.post("/{board_id}/pins")
async def add_pin_to_board(
    board_id: int = Path(..., description="Board ID"),
    pin_data: AddPinToBoard = ...,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Add a pin to a board"""
    success = BoardService.add_pin_to_board(db, board_id, pin_data.pin_id, current_user)
    if success:
        return {"message": "Pin added to board successfully"}
    else:
        return {"message": "Pin is already in this board"}

@router.delete("/{board_id}/pins")
async def remove_pin_from_board(
    board_id: int = Path(..., description="Board ID"),
    pin_data: RemovePinFromBoard = ...,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Remove a pin from a board"""
    success = BoardService.remove_pin_from_board(db, board_id, pin_data.pin_id, current_user)
    if not success:
        raise HTTPException(status_code=404, detail="Pin not found in board or not authorized")
    
    return {"message": "Pin removed from board successfully"}
