import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'screens/home_screen.dart';
import 'screens/pin_details_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/create_pin_screen.dart';
import 'screens/login_screen.dart';
import 'theme/app_theme.dart';
import 'providers/pin_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/board_provider.dart';

void main() {
  runApp(const PinterestApp());
}

class PinterestApp extends StatelessWidget {
  const PinterestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => PinProvider()),
        ChangeNotifierProvider(create: (_) => BoardProvider()),
      ],
      child: MaterialApp.router(
        title: 'Pinterest Clone',
        theme: AppTheme.darkTheme,
        routerConfig: _router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

final GoRouter _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/pin/:id',
      builder: (context, state) => PinDetailsScreen(
        pinId: state.pathParameters['id']!,
      ),
    ),
    GoRoute(
      path: '/profile/:username',
      builder: (context, state) => ProfileScreen(
        username: state.pathParameters['username']!,
      ),
    ),
    GoRoute(
      path: '/create',
      builder: (context, state) => const CreatePinScreen(),
    ),
  ],
);


