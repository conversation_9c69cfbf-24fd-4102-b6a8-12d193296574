from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from fastapi import HTTPException, status

from app.models.models import Pin, User, Comment, pin_likes
from app.schemas.pin import Pin<PERSON><PERSON>, PinUp<PERSON>, Pin as PinSchema, PinList

class PinService:
    @staticmethod
    def create_pin(db: Session, pin_create: PinCreate, user: User) -> Pin:
        """Create a new pin"""
        db_pin = Pin(
            title=pin_create.title,
            description=pin_create.description,
            image_url=pin_create.image_url,
            destination_url=pin_create.destination_url,
            aspect_ratio=pin_create.aspect_ratio,
            is_public=pin_create.is_public,
            user_id=user.id
        )
        
        db.add(db_pin)
        db.commit()
        db.refresh(db_pin)
        return db_pin

    @staticmethod
    def get_pin(db: Session, pin_id: int, current_user: Optional[User] = None) -> Optional[Pin]:
        """Get a pin by ID"""
        pin = db.query(Pin).filter(Pin.id == pin_id).first()
        if not pin:
            return None
        
        # Check if pin is public or user owns it
        if not pin.is_public and (not current_user or pin.user_id != current_user.id):
            return None
        
        return pin

    @staticmethod
    def get_pins(
        db: Session, 
        skip: int = 0, 
        limit: int = 20,
        user_id: Optional[int] = None,
        current_user: Optional[User] = None
    ) -> PinList:
        """Get list of pins with pagination"""
        query = db.query(Pin)
        
        # Filter by user if specified
        if user_id:
            query = query.filter(Pin.user_id == user_id)
            # If viewing another user's pins, only show public ones
            if not current_user or current_user.id != user_id:
                query = query.filter(Pin.is_public == True)
        else:
            # Only show public pins for general feed
            query = query.filter(Pin.is_public == True)
        
        # Get total count
        total = query.count()
        
        # Get pins with pagination
        pins = query.order_by(desc(Pin.created_at)).offset(skip).limit(limit).all()
        
        pin_schemas = []
        for pin in pins:
            pin_schema = PinService._pin_to_schema(pin, current_user, db)
            pin_schemas.append(pin_schema)

        return PinList(
            pins=pin_schemas,
            total=total,
            page=skip // limit + 1,
            size=limit,
            has_next=skip + limit < total
        )

    @staticmethod
    def update_pin(db: Session, pin_id: int, pin_update: PinUpdate, user: User) -> Optional[Pin]:
        """Update a pin"""
        pin = db.query(Pin).filter(Pin.id == pin_id, Pin.user_id == user.id).first()
        if not pin:
            return None
        
        update_data = pin_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(pin, field, value)
        
        db.commit()
        db.refresh(pin)
        return pin

    @staticmethod
    def delete_pin(db: Session, pin_id: int, user: User) -> bool:
        """Delete a pin"""
        pin = db.query(Pin).filter(Pin.id == pin_id, Pin.user_id == user.id).first()
        if not pin:
            return False
        
        db.delete(pin)
        db.commit()
        return True

    @staticmethod
    def like_pin(db: Session, pin_id: int, user: User) -> bool:
        """Like/unlike a pin"""
        pin = db.query(Pin).filter(Pin.id == pin_id).first()
        if not pin:
            raise HTTPException(status_code=404, detail="Pin not found")
        
        # Check if already liked
        existing_like = db.query(pin_likes).filter(
            pin_likes.c.user_id == user.id,
            pin_likes.c.pin_id == pin_id
        ).first()
        
        if existing_like:
            # Unlike
            db.execute(
                pin_likes.delete().where(
                    pin_likes.c.user_id == user.id,
                    pin_likes.c.pin_id == pin_id
                )
            )
            db.commit()
            return False
        else:
            # Like
            db.execute(
                pin_likes.insert().values(user_id=user.id, pin_id=pin_id)
            )
            db.commit()
            return True

    @staticmethod
    def _pin_to_schema(pin: Pin, current_user: Optional[User], db: Session) -> PinSchema:
        """Convert Pin model to schema"""
        # Count likes and comments
        like_count = db.query(func.count(pin_likes.c.user_id)).filter(
            pin_likes.c.pin_id == pin.id
        ).scalar() or 0
        
        comment_count = db.query(func.count(Comment.id)).filter(
            Comment.pin_id == pin.id
        ).scalar() or 0
        
        # Check if current user liked this pin
        is_liked = False
        if current_user:
            is_liked = db.query(pin_likes).filter(
                pin_likes.c.user_id == current_user.id,
                pin_likes.c.pin_id == pin.id
            ).first() is not None
        
        return PinSchema(
            id=pin.id,
            title=pin.title,
            description=pin.description,
            image_url=pin.image_url,
            destination_url=pin.destination_url,
            aspect_ratio=pin.aspect_ratio,
            is_public=pin.is_public,
            user_id=pin.user_id,
            created_at=pin.created_at,
            updated_at=pin.updated_at,
            user=pin.user,
            like_count=like_count,
            comment_count=comment_count,
            is_liked=is_liked
        )
