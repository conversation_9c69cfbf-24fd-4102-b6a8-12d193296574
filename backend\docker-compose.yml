version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql://postgres:[YOUR-PASSWORD]@db.krnaiyjplgzawfrbapji.supabase.co:5432/postgres
      SUPABASE_URL: https://krnaiyjplgzawfrbapji.supabase.co
      SUPABASE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtybmFpeWpwbGd6YXdmcmJhcGppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyOTcwMDIsImV4cCI6MjA2Njg3MzAwMn0.4xCnhn4XaU5DRevqkO4nnJpR0LRGzQ-rk-vDCiwru6M
      SECRET_KEY: your-super-secret-key-change-this-in-production
      DEBUG: "True"
    volumes:
      - ./uploads:/app/uploads
