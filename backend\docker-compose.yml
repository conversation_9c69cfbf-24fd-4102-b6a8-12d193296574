version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: pinterest_clone
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: **************************************/pinterest_clone
      SECRET_KEY: your-super-secret-key-change-this-in-production
      DEBUG: "True"
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads

volumes:
  postgres_data:
