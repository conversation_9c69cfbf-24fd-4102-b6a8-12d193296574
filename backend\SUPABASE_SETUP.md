# Supabase Setup Guide

## 🚀 Quick Setup with Supabase

Your backend is now configured to use Supabase as the database. Here's how to get started:

### 1. Get Your Supabase Database Password

1. Go to your Supabase project: https://krnaiyjplgzawfrbapji.supabase.co
2. Navigate to **Settings** → **Database**
3. Copy your database password (you set this when creating the project)

### 2. Configure Environment Variables

Create a `.env` file in the backend directory:

```bash
cp .env.example .env
```

Edit the `.env` file and replace `[YOUR-PASSWORD]` with your actual Supabase database password:

```env
# Supabase Database
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
SUPABASE_URL=https://krnaiyjplgzawfrbapji.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.4xCnhn4XaU5DRevqkO4nnJpR0LRGzQ-rk-vDCiwru6M

# JWT
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AWS S3 (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-bucket-name
AWS_REGION=us-east-1

# App Settings
DEBUG=True
```

### 3. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 4. Run Database Migrations

```bash
# Update alembic.ini with your password first
alembic upgrade head
```

### 5. Create Sample Data

```bash
python scripts/create_sample_data.py
```

### 6. Start the Server

```bash
python run.py
```

## 🐳 Docker Setup with Supabase

Update your docker-compose.yml environment variables:

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql://postgres:<EMAIL>:5432/postgres
      SUPABASE_URL: https://krnaiyjplgzawfrbapji.supabase.co
      SUPABASE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.4xCnhn4XaU5DRevqkO4nnJpR0LRGzQ-rk-vDCiwru6M
      SECRET_KEY: your-super-secret-key-change-this-in-production
      DEBUG: "True"
    volumes:
      - ./uploads:/app/uploads
```

Then run:
```bash
docker-compose up --build
```

## 🔧 Supabase Configuration

### Database Tables

The following tables will be created automatically when you run migrations:

- `users` - User accounts and profiles
- `pins` - Pin posts with images and metadata
- `boards` - Collections of pins
- `comments` - Comments on pins
- `pin_likes` - Like relationships
- `user_follows` - Follow relationships
- `board_pins` - Many-to-many relationship between boards and pins

### Row Level Security (RLS)

For production, you may want to enable RLS in Supabase:

1. Go to **Authentication** → **Policies**
2. Enable RLS for each table
3. Create policies based on your security requirements

Example policy for pins table:
```sql
-- Users can view all public pins
CREATE POLICY "Public pins are viewable by everyone" ON pins
FOR SELECT USING (is_public = true);

-- Users can insert their own pins
CREATE POLICY "Users can insert their own pins" ON pins
FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Users can update their own pins
CREATE POLICY "Users can update their own pins" ON pins
FOR UPDATE USING (auth.uid()::text = user_id::text);
```

### Supabase Storage (Optional)

Instead of AWS S3, you can use Supabase Storage for images:

1. Go to **Storage** in your Supabase dashboard
2. Create a bucket called `pin-images`
3. Set appropriate policies for public access
4. Update the upload service to use Supabase Storage

## 🚀 Deployment

### Railway with Supabase

1. Connect your GitHub repo to Railway
2. Set environment variables:
   ```
   DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
   SUPABASE_URL=https://krnaiyjplgzawfrbapji.supabase.co
   SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.4xCnhn4XaU5DRevqkO4nnJpR0LRGzQ-rk-vDCiwru6M
   SECRET_KEY=your-production-secret-key
   ```
3. Deploy automatically

### Render with Supabase

1. Create a Web Service on Render
2. Build command: `pip install -r requirements.txt`
3. Start command: `gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT`
4. Set the same environment variables as above

## 🔍 Verify Setup

1. **Check API Health:**
   ```bash
   curl http://localhost:8000/health
   ```

2. **Check Database Connection:**
   ```bash
   curl http://localhost:8000/api/v1/pins/
   ```

3. **Register a Test User:**
   ```bash
   curl -X POST "http://localhost:8000/api/v1/auth/register" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "testuser",
       "email": "<EMAIL>", 
       "display_name": "Test User",
       "password": "password123"
     }'
   ```

## 🎯 Benefits of Using Supabase

✅ **Managed Database** - No need to manage PostgreSQL yourself
✅ **Built-in Auth** - Can integrate with Supabase Auth later
✅ **Real-time** - Built-in real-time subscriptions
✅ **Storage** - Built-in file storage
✅ **Dashboard** - Easy database management
✅ **Automatic Backups** - Built-in backup system
✅ **Global CDN** - Fast worldwide access

## 🔧 Troubleshooting

**Connection Issues:**
- Make sure your Supabase project is active
- Verify the database password is correct
- Check if your IP is allowed (Supabase allows all by default)

**Migration Issues:**
- Make sure the DATABASE_URL in alembic.ini has the correct password
- Run migrations with: `alembic upgrade head`

**Authentication Issues:**
- Verify the SUPABASE_KEY is correct
- Check that the SECRET_KEY is set for JWT tokens

Your Pinterest clone backend is now ready to use Supabase! 🎉
