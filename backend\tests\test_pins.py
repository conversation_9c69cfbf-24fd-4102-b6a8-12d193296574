import pytest
from fastapi.testclient import TestClient

def test_create_pin(client: TestClient, auth_headers):
    """Test creating a pin"""
    response = client.post(
        "/api/v1/pins/",
        json={
            "title": "Test Pin",
            "description": "A test pin description",
            "image_url": "https://example.com/image.jpg",
            "aspect_ratio": 0.75,
            "is_public": True
        },
        headers=auth_headers
    )
    assert response.status_code == 201
    data = response.json()
    assert data["title"] == "Test Pin"
    assert data["description"] == "A test pin description"
    assert "id" in data

def test_get_pins(client: TestClient):
    """Test getting pins list"""
    response = client.get("/api/v1/pins/")
    assert response.status_code == 200
    data = response.json()
    assert "pins" in data
    assert "total" in data
    assert "page" in data

def test_create_pin_unauthorized(client: TestClient):
    """Test creating pin without authentication"""
    response = client.post(
        "/api/v1/pins/",
        json={
            "title": "Test Pin",
            "description": "A test pin description",
            "image_url": "https://example.com/image.jpg"
        }
    )
    assert response.status_code == 401
