from typing import List
from decouple import config

class Settings:
    # Supabase Database
    DATABASE_URL: str = config("DATABASE_URL", default="postgresql://postgres:[YOUR-PASSWORD]@db.krnaiyjplgzawfrbapji.supabase.co:5432/postgres")
    SUPABASE_URL: str = config("SUPABASE_URL", default="https://krnaiyjplgzawfrbapji.supabase.co")
    SUPABASE_KEY: str = config("SUPABASE_KEY", default="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtybmFpeWpwbGd6YXdmcmJhcGppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyOTcwMDIsImV4cCI6MjA2Njg3MzAwMn0.4xCnhn4XaU5DRevqkO4nnJpR0LRGzQ-rk-vDCiwru6M")
    
    # JWT
    SECRET_KEY: str = config("SECRET_KEY", default="your-super-secret-key-change-this-in-production")
    ALGORITHM: str = config("ALGORITHM", default="HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = config("ACCESS_TOKEN_EXPIRE_MINUTES", default=30, cast=int)
    
    # AWS S3
    AWS_ACCESS_KEY_ID: str = config("AWS_ACCESS_KEY_ID", default="")
    AWS_SECRET_ACCESS_KEY: str = config("AWS_SECRET_ACCESS_KEY", default="")
    AWS_BUCKET_NAME: str = config("AWS_BUCKET_NAME", default="")
    AWS_REGION: str = config("AWS_REGION", default="us-east-1")
    
    # App Settings
    DEBUG: bool = config("DEBUG", default=True, cast=bool)
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ]
    
    # File Upload
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_IMAGE_TYPES: List[str] = ["image/jpeg", "image/png", "image/webp"]
    UPLOAD_DIR: str = "uploads"

settings = Settings()
