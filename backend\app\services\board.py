from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc
from fastapi import HTTPException, status

from app.models.models import Board, User, Pin, board_pins
from app.schemas.board import BoardCreate, BoardUpdate, Board as BoardSchema, BoardWithPins, BoardList

class BoardService:
    @staticmethod
    def create_board(db: Session, board_create: BoardCreate, user: User) -> BoardSchema:
        """Create a new board"""
        db_board = Board(
            name=board_create.name,
            description=board_create.description,
            is_secret=board_create.is_secret,
            user_id=user.id
        )
        
        db.add(db_board)
        db.commit()
        db.refresh(db_board)
        
        return BoardService._board_to_schema(db_board, db)

    @staticmethod
    def get_user_boards(db: Session, user_id: int, current_user: Optional[User] = None) -> BoardList:
        """Get all boards for a user"""
        query = db.query(Board).filter(Board.user_id == user_id)
        
        # If viewing another user's boards, only show public ones
        if not current_user or current_user.id != user_id:
            query = query.filter(Board.is_secret == False)
        
        boards = query.order_by(desc(Board.created_at)).all()
        
        board_schemas = [BoardService._board_to_schema(board, db) for board in boards]
        
        return BoardList(
            boards=board_schemas,
            total=len(board_schemas)
        )

    @staticmethod
    def get_board(db: Session, board_id: int, current_user: Optional[User] = None) -> Optional[BoardWithPins]:
        """Get a board with its pins"""
        board = db.query(Board).filter(Board.id == board_id).first()
        if not board:
            return None
        
        # Check if board is public or user owns it
        if board.is_secret and (not current_user or board.user_id != current_user.id):
            return None
        
        return BoardService._board_with_pins_to_schema(board, db, current_user)

    @staticmethod
    def update_board(db: Session, board_id: int, board_update: BoardUpdate, user: User) -> Optional[BoardSchema]:
        """Update a board"""
        board = db.query(Board).filter(Board.id == board_id, Board.user_id == user.id).first()
        if not board:
            return None
        
        update_data = board_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(board, field, value)
        
        db.commit()
        db.refresh(board)
        
        return BoardService._board_to_schema(board, db)

    @staticmethod
    def delete_board(db: Session, board_id: int, user: User) -> bool:
        """Delete a board"""
        board = db.query(Board).filter(Board.id == board_id, Board.user_id == user.id).first()
        if not board:
            return False
        
        db.delete(board)
        db.commit()
        return True

    @staticmethod
    def add_pin_to_board(db: Session, board_id: int, pin_id: int, user: User) -> bool:
        """Add a pin to a board"""
        # Check if user owns the board
        board = db.query(Board).filter(Board.id == board_id, Board.user_id == user.id).first()
        if not board:
            raise HTTPException(status_code=404, detail="Board not found or not authorized")
        
        # Check if pin exists and is accessible
        pin = db.query(Pin).filter(Pin.id == pin_id).first()
        if not pin:
            raise HTTPException(status_code=404, detail="Pin not found")
        
        if not pin.is_public and pin.user_id != user.id:
            raise HTTPException(status_code=403, detail="Pin is not accessible")
        
        # Check if pin is already in board
        existing = db.query(board_pins).filter(
            board_pins.c.board_id == board_id,
            board_pins.c.pin_id == pin_id
        ).first()
        
        if existing:
            return False  # Already in board
        
        # Add pin to board
        db.execute(
            board_pins.insert().values(board_id=board_id, pin_id=pin_id)
        )
        db.commit()
        return True

    @staticmethod
    def remove_pin_from_board(db: Session, board_id: int, pin_id: int, user: User) -> bool:
        """Remove a pin from a board"""
        # Check if user owns the board
        board = db.query(Board).filter(Board.id == board_id, Board.user_id == user.id).first()
        if not board:
            return False
        
        # Remove pin from board
        result = db.execute(
            board_pins.delete().where(
                board_pins.c.board_id == board_id,
                board_pins.c.pin_id == pin_id
            )
        )
        db.commit()
        
        return result.rowcount > 0

    @staticmethod
    def _board_to_schema(board: Board, db: Session) -> BoardSchema:
        """Convert Board model to schema"""
        # Count pins in board
        pin_count = db.query(board_pins).filter(board_pins.c.board_id == board.id).count()
        
        # Get preview pins (first 3)
        preview_pin_ids = db.query(board_pins.c.pin_id).filter(
            board_pins.c.board_id == board.id
        ).limit(3).all()
        
        preview_pins = []
        if preview_pin_ids:
            from app.services.pin import PinService
            pins = db.query(Pin).filter(Pin.id.in_([pid[0] for pid in preview_pin_ids])).all()
            preview_pins = [PinService._pin_to_schema(pin, None, db) for pin in pins]
        
        return BoardSchema(
            id=board.id,
            name=board.name,
            description=board.description,
            is_secret=board.is_secret,
            user_id=board.user_id,
            created_at=board.created_at,
            updated_at=board.updated_at,
            user=board.user,
            pin_count=pin_count,
            preview_pins=preview_pins
        )

    @staticmethod
    def _board_with_pins_to_schema(board: Board, db: Session, current_user: Optional[User]) -> BoardWithPins:
        """Convert Board model to schema with all pins"""
        # Get all pins in board
        pin_ids = db.query(board_pins.c.pin_id).filter(board_pins.c.board_id == board.id).all()
        
        pins = []
        if pin_ids:
            from app.services.pin import PinService
            pin_objects = db.query(Pin).filter(Pin.id.in_([pid[0] for pid in pin_ids])).all()
            pins = [PinService._pin_to_schema(pin, current_user, db) for pin in pin_objects]
        
        return BoardWithPins(
            id=board.id,
            name=board.name,
            description=board.description,
            is_secret=board.is_secret,
            user_id=board.user_id,
            created_at=board.created_at,
            updated_at=board.updated_at,
            user=board.user,
            pin_count=len(pins),
            preview_pins=pins[:3],
            pins=pins
        )
