# 🧪 Full-Stack Testing Guide

## 🚀 Complete Setup & Testing Instructions

### **Step 1: Setup Backend (Supabase)**

1. **Navigate to backend directory:**
```bash
cd backend
```

2. **Quick Supabase setup:**
```bash
python scripts/setup_supabase.py
```
*Enter your Supabase database password when prompted*

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Run migrations:**
```bash
alembic upgrade head
```

5. **Create sample data:**
```bash
python scripts/create_sample_data.py
```

6. **Start backend server:**
```bash
python run.py
```

**Backend should now be running at: http://localhost:8000**

### **Step 2: Test Backend API**

In a new terminal, test the backend:
```bash
cd backend
python test_api.py
```

You should see:
```
✅ Health check: {'status': 'healthy'}
✅ Get pins: Found X pins
✅ Login successful: Got token
✅ Create pin successful: Pin ID X
```

### **Step 3: Setup Frontend (Flutter)**

1. **Navigate to Flutter directory:**
```bash
cd ..  # Go back to root if in backend folder
```

2. **Get Flutter dependencies:**
```bash
flutter pub get
```

3. **Start Flutter web app:**
```bash
flutter run -d chrome --web-renderer html
```

**Frontend should now be running at: http://localhost:8080**

### **Step 4: Test Full Integration**

#### **4.1 Test Home Feed**
1. Open http://localhost:8080
2. You should see the Pinterest-style dark theme interface
3. Pins should load from the backend API
4. Check browser console for any errors

#### **4.2 Test Authentication**
1. Click the "Login" button in the top navigation
2. Try logging in with sample user:
   - **Username:** `sarah_designs`
   - **Password:** `password123`
3. Or click "Login as Sample User" for quick login
4. Should redirect to home page after successful login

#### **4.3 Test Pin Creation**
1. After logging in, navigate to `/create`
2. Fill out the pin creation form
3. Test image upload functionality
4. Verify pin appears in the feed

#### **4.4 Test User Profiles**
1. Click on a user's avatar or name
2. Should show user profile with their pins and boards
3. Test follow/unfollow functionality

#### **4.5 Test Pin Details**
1. Click on any pin
2. Should show detailed pin view
3. Test like functionality
4. Test commenting system

## 🔧 **API Endpoints to Test**

### **Public Endpoints (No Auth Required)**
- `GET /health` - Health check
- `GET /api/v1/pins/` - Get pins feed
- `GET /api/v1/pins/{id}` - Get pin details
- `POST /api/v1/auth/register` - Register user
- `POST /api/v1/auth/login-json` - Login user

### **Protected Endpoints (Auth Required)**
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/pins/` - Create pin
- `POST /api/v1/pins/{id}/like` - Like/unlike pin
- `POST /api/v1/boards/` - Create board
- `POST /api/v1/users/{id}/follow` - Follow user
- `POST /api/v1/upload/image` - Upload image

## 🧪 **Manual Testing Checklist**

### **Backend Tests**
- [ ] Health endpoint responds
- [ ] Database connection works
- [ ] Sample data loads correctly
- [ ] User registration works
- [ ] User login works
- [ ] JWT tokens are generated
- [ ] Protected endpoints require auth
- [ ] Pin CRUD operations work
- [ ] Board CRUD operations work
- [ ] Image upload works
- [ ] Like/unlike functionality works
- [ ] Follow/unfollow works
- [ ] Comments system works

### **Frontend Tests**
- [ ] App loads without errors
- [ ] Dark theme displays correctly
- [ ] Masonry grid layout works
- [ ] Pin cards display properly
- [ ] Navigation works
- [ ] Login screen functions
- [ ] Authentication flow works
- [ ] API integration works
- [ ] Error handling works
- [ ] Responsive design works
- [ ] Hover effects work
- [ ] Routing works correctly

### **Integration Tests**
- [ ] Frontend connects to backend
- [ ] Login flow end-to-end
- [ ] Pin creation end-to-end
- [ ] Pin viewing end-to-end
- [ ] User profile viewing
- [ ] Real-time data updates
- [ ] Error states display properly

## 🐛 **Troubleshooting**

### **Backend Issues**

**"Connection refused" errors:**
```bash
# Check if backend is running
curl http://localhost:8000/health

# If not running, start it:
cd backend
python run.py
```

**Database connection errors:**
```bash
# Check your .env file has correct Supabase password
# Verify Supabase project is active
# Run migrations again:
alembic upgrade head
```

**Import errors:**
```bash
# Make sure you're in the backend directory
cd backend
pip install -r requirements.txt
```

### **Frontend Issues**

**"XMLHttpRequest error" in browser:**
- Make sure backend is running on http://localhost:8000
- Check browser console for CORS errors
- Verify API endpoints are accessible

**Flutter build errors:**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run -d chrome --web-renderer html
```

**Dependency issues:**
```bash
# Update dependencies
flutter pub upgrade
```

### **CORS Issues**

If you see CORS errors in browser console:
1. Backend CORS is already configured for localhost:8080
2. Make sure you're accessing Flutter app on the correct port
3. Check backend logs for CORS-related messages

## 📱 **Testing on Different Platforms**

### **Web (Chrome)**
```bash
flutter run -d chrome --web-renderer html
```

### **Mobile (Android/iOS)**
```bash
# For Android
flutter run -d android

# For iOS (Mac only)
flutter run -d ios
```

**Note:** For mobile testing, update the API base URL in `lib/services/api_service.dart` to use your computer's IP address instead of localhost.

## 🎯 **Expected Results**

### **Successful Backend Setup**
- Server starts without errors
- Database migrations complete
- Sample data creates successfully
- API endpoints respond correctly
- Authentication works

### **Successful Frontend Setup**
- App compiles without errors
- Dark theme displays correctly
- Pinterest-style layout appears
- Navigation works smoothly
- API calls succeed

### **Successful Integration**
- Pins load from backend
- Login/logout works
- Pin creation works
- Real-time updates work
- Error handling works

## 🚀 **Next Steps After Testing**

1. **Deploy Backend:**
   - Railway: Connect GitHub repo
   - Render: Deploy with environment variables
   - AWS: Use Docker deployment

2. **Deploy Frontend:**
   - Vercel: Connect GitHub repo
   - Firebase Hosting: Build and deploy
   - Netlify: Static site deployment

3. **Production Configuration:**
   - Update API URLs for production
   - Configure proper CORS origins
   - Set up SSL certificates
   - Configure environment variables

4. **Additional Features:**
   - Real-time notifications
   - Advanced search
   - Image optimization
   - Caching strategies
   - Analytics integration

## 📞 **Support**

If you encounter issues:
1. Check the console logs (both backend and frontend)
2. Verify all services are running
3. Check network connectivity
4. Review the troubleshooting section
5. Ensure all dependencies are installed

**Happy Testing! 🎉**
