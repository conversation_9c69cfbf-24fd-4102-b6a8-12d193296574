version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: pinterest_clone
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/pinterest_clone
      SECRET_KEY: ${SECRET_KEY}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_BUCKET_NAME: ${AWS_BUCKET_NAME}
      AWS_REGION: ${AWS_REGION}
      DEBUG: "False"
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    command: ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]

volumes:
  postgres_data:
