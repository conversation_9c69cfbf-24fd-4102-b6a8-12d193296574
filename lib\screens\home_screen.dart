import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../providers/pin_provider.dart';
import '../widgets/top_navigation_bar.dart';
import '../widgets/masonry_grid.dart';
import '../theme/app_theme.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Top Navigation Bar
          const SliverToBoxAdapter(
            child: TopNavigationBar(),
          ),
          
          // Main Content
          SliverToBoxAdapter(
            child: Consumer<PinProvider>(
              builder: (context, pinProvider, child) {
                if (pinProvider.isLoading) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(50.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                return MasonryGrid(pins: pinProvider.pins);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/create'),
        backgroundColor: AppTheme.pinterestRed,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}
