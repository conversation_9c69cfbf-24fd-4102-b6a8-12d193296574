#!/bin/bash

# Start script for production deployment

echo "Starting Pinterest Clone Backend..."

# Run database migrations
echo "Running database migrations..."
alembic upgrade head

# Create sample data if needed
if [ "$CREATE_SAMPLE_DATA" = "true" ]; then
    echo "Creating sample data..."
    python scripts/create_sample_data.py
fi

# Start the application
echo "Starting application..."
if [ "$ENVIRONMENT" = "production" ]; then
    gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
else
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
fi
