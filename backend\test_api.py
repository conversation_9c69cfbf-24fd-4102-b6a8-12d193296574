#!/usr/bin/env python3
"""
Quick API test script to verify backend is working
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"✅ Health check: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_get_pins():
    """Test getting pins"""
    try:
        response = requests.get(f"{BASE_URL}/pins/")
        data = response.json()
        print(f"✅ Get pins: Found {len(data.get('pins', []))} pins")
        return True
    except Exception as e:
        print(f"❌ Get pins failed: {e}")
        return False

def test_login():
    """Test login with sample user"""
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login-json",
            json={
                "username": "sarah_designs",
                "password": "password123"
            }
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful: Got token")
            return data.get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login failed: {e}")
        return None

def test_create_pin(token):
    """Test creating a pin"""
    try:
        response = requests.post(
            f"{BASE_URL}/pins/",
            headers={"Authorization": f"Bearer {token}"},
            json={
                "title": "Test Pin from API",
                "description": "This pin was created via API test",
                "image_url": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=600",
                "aspect_ratio": 0.75,
                "is_public": True
            }
        )
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Create pin successful: Pin ID {data.get('id')}")
            return True
        else:
            print(f"❌ Create pin failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Create pin failed: {e}")
        return False

def main():
    print("🧪 Testing Pinterest Clone Backend API")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("\n❌ Backend is not running! Start it with: python run.py")
        return
    
    # Test get pins
    test_get_pins()
    
    # Test login
    token = test_login()
    
    if token:
        # Test authenticated endpoints
        test_create_pin(token)
    
    print("\n🎉 API testing complete!")
    print("\nNext steps:")
    print("1. Start Flutter app: flutter run -d chrome")
    print("2. Navigate to login page")
    print("3. Login with: sarah_designs / password123")
    print("4. Test creating pins and boards")

if __name__ == "__main__":
    main()
